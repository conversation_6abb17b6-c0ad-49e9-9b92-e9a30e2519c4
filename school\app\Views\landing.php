
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestionBank Pro - SaaS Question Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .tech-badge {
            transition: all 0.3s ease;
        }

        .tech-badge:hover {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50">
     <!-- logout message -->
      <?php if (session()->getFlashdata('message')) : ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded mb-4 text-center">
        <?= session()->getFlashdata('message') ?>
    </div>
<?php endif; ?>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i class="fas fa-graduation-cap text-3xl text-indigo-600 mr-3"></i>
                    <span class="text-2xl font-bold text-gray-800">QuestionBank Pro</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-indigo-600 transition duration-300">Features</a>
                    <a href="#technology" class="text-gray-600 hover:text-indigo-600 transition duration-300">Technology</a>
                    <a href="#pricing" class="text-gray-600 hover:text-indigo-600 transition duration-300">Pricing</a>
                    <a href="#footer" class="text-gray-600 hover:text-indigo-600 transition duration-300">Support</a>
                </div>
                <div class="flex space-x-4">
                    <div class="relative inline-block text-left">
                        <button id="loginToggle"
                                class="inline-flex justify-center w-full rounded-lg bg-indigo-600 text-white px-6 py-2 font-medium hover:bg-indigo-700">
                            Login
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                 viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <div id="loginMenu"
                             class="hidden absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded shadow-lg z-50">
                            <button onclick="openLoginModal('superadmin')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-shield mr-2"></i>SuperAdmin Login
                            </button>
                            <button onclick="openLoginModal('schooladmin')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-school mr-2"></i>School Admin Login
                            </button>
                            <button onclick="openLoginModal('staff')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-tie mr-2"></i>Staff Login
                            </button>
                        </div>
                    </div>
                    <button data-register-modal class="bg-indigo-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-indigo-700 transition duration-300">
    Register
</button>
                </div>
            </div>
        </div>
    </nav>
    <!-- Hero Section -->
    <section class="gradient-bg text-white pt-24 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    Streamline Your Question Bank Management
                </h1>
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                    A comprehensive SaaS platform for educational institutions to create, manage, and organize
                    question banks with multi-tenant architecture and role-based access control.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button data-register-modal class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition duration-300">
                        <i class="fas fa-play mr-2"></i>Start Free Trial
                    </button>
                    <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300">
                        <i class="fas fa-info-circle mr-2"></i>Learn More
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- Key Benefits -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Why Choose QuestionBank Pro?</h2>
                <p class="text-xl text-gray-600">Built specifically for educational institutions with security and
                    scalability in mind</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-building text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Multi-Tenant Architecture</h3>
                    <p class="text-gray-600">Each school operates independently with complete data isolation and
                        customized settings.</p>
                </div>
                <div class="text-center p-6">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Role-Based Security</h3>
                    <p class="text-gray-600">Granular permissions for Super Admins, School Admins, and staff with
                        audit logging.</p>
                </div>
                <div class="text-center p-6">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-question-circle text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Comprehensive Question Management</h3>
                    <p class="text-gray-600">Support for MCQs and descriptive questions with subject categorization
                        and difficulty levels.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600">Everything you need to manage your institution's question banks
                    effectively</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-users text-2xl text-indigo-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">User & Role Management</h3>
                    </div>
                    <p class="text-gray-600">Dynamic permission assignment with easy user onboarding for schools and
                        staff members.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-book text-2xl text-green-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Subject Management</h3>
                    </div>
                    <p class="text-gray-600">Create and organize subjects within each school's scope with hierarchical
                        categorization.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-history text-2xl text-purple-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Audit Logging</h3>
                    </div>
                    <p class="text-gray-600">Complete tracking of user actions including create, update, and delete
                        operations for accountability.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-credit-card text-2xl text-blue-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Subscription Management</h3>
                    </div>
                    <p class="text-gray-600">Flexible trial and paid subscription system with automatic access control
                        based on subscription status.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-lock text-2xl text-red-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Secure Authentication</h3>
                    </div>
                    <p class="text-gray-600">Robust login system with session management and middleware-based access
                        control.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-chart-line text-2xl text-orange-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Scalable Architecture</h3>
                    </div>
                    <p class="text-gray-600">Built on CodeIgniter 4 with MySQL for high performance and scalability
                        across multiple institutions.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- User Roles Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">User Roles & Permissions</h2>
                <p class="text-xl text-gray-600">Designed exclusively for internal academic content management</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-crown text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">Super Admin</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Manages all schools</li>
                            <li><i class="fas fa-check mr-2"></i>Global role management</li>
                            <li><i class="fas fa-check mr-2"></i>System settings control</li>
                            <li><i class="fas fa-check mr-2"></i>Subscription oversight</li>
                        </ul>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-user-tie text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">School Admin</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Manages school staff</li>
                            <li><i class="fas fa-check mr-2"></i>Academic data oversight</li>
                            <li><i class="fas fa-check mr-2"></i>Subject management</li>
                            <li><i class="fas fa-check mr-2"></i>School-level reporting</li>
                        </ul>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-chalkboard-teacher text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">Staff/Faculty</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Add & manage questions</li>
                            <li><i class="fas fa-check mr-2"></i>Subject content creation</li>
                            <li><i class="fas fa-check mr-2"></i>Question categorization</li>
                            <li><i class="fas fa-check mr-2"></i>Content collaboration</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="text-center mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <p class="text-yellow-800 font-semibold">
                    <i class="fas fa-info-circle mr-2"></i>
                    Note: This platform is designed exclusively for internal academic content management. No student
                    access is provided.
                </p>
            </div>
        </div>
    </section>
    <!-- Technology Stack -->
    <section id="technology" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Built with Modern Technology</h2>
                <p class="text-xl text-gray-600">Reliable, secure, and scalable technology stack</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-php text-4xl text-purple-600 mb-3"></i>
                    <h4 class="font-semibold">PHP</h4>
                    <p class="text-sm text-gray-600">CodeIgniter 4</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fas fa-database text-4xl text-blue-600 mb-3"></i>
                    <h4 class="font-semibold">MySQL</h4>
                    <p class="text-sm text-gray-600">Database</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-bootstrap text-4xl text-purple-500 mb-3"></i>
                    <h4 class="font-semibold">Bootstrap</h4>
                    <p class="text-sm text-gray-600">Frontend</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-git-alt text-4xl text-orange-600 mb-3"></i>
                    <h4 class="font-semibold">Git</h4>
                    <p class="text-sm text-gray-600">Version Control</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Pricing Section -->
    <section id="pricing" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Simple, Transparent Pricing</h2>
                <p class="text-xl text-gray-600">Start with a free trial, upgrade when you're ready</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Free Trial Plan -->
                <div class="bg-white p-8 rounded-xl border-2 border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex flex-col">
                    <div class="text-center flex-grow">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">Free Trial</h3>
                        <div class="text-4xl font-bold text-gray-800 mb-2">₹0</div>
                        <p class="text-gray-600 mb-6">30 days free trial</p>
                        <ul class="text-left space-y-3 mb-8 flex-grow">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Up to 50 questions</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>5 subjects maximum</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>5 users maximum</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Basic printing</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Email support</span></li>
                        </ul>
                    </div>
                    <div class="mt-auto">
                        <button data-register-modal data-plan="free" class="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transform hover:scale-105 transition-all duration-300 shadow-md hover:shadow-lg">
                            <i class="fas fa-gift mr-2"></i>Start Free Trial
                        </button>
                    </div>
                </div>

                <!-- Professional Plan -->
                <div class="bg-white p-8 rounded-xl border-2 border-indigo-500 hover:border-indigo-600 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 relative flex flex-col">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-indigo-500 text-white px-4 py-1 rounded-full text-sm font-semibold shadow-lg">Most Popular</span>
                    </div>
                    <div class="text-center flex-grow">
                        <h3 class="text-2xl font-bold mb-4 text-indigo-600">Professional</h3>
                        <div class="text-4xl font-bold text-indigo-600 mb-2">₹999</div>
                        <p class="text-gray-600 mb-6">per school/month</p>
                        <ul class="text-left space-y-3 mb-8 flex-grow">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Unlimited questions</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Unlimited subjects</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Up to 50 users</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Export & Print</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Audit logging</span></li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i><span>Priority support</span></li>
                        </ul>
                    </div>
                    <div class="mt-auto">
                        <button data-register-modal data-plan="professional" class="w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-md hover:shadow-lg">
                            <i class="fas fa-rocket mr-2"></i>Get Started
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- CTA Section -->
    <section class="gradient-bg text-white py-16">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl font-bold mb-6">Ready to Transform Your Question Bank Management?</h2>
            <p class="text-xl mb-8 opacity-90">Join educational institutions worldwide who trust QuestionBank Pro for
                their academic content management needs.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button data-register-modal class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition duration-300">
                    <i class="fas fa-rocket mr-2"></i>Start Your Free Trial
                </button>
                <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300">
                    <i class="fas fa-calendar mr-2"></i>Schedule Demo
                </button>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer id="footer" class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <i class="fas fa-graduation-cap text-2xl text-indigo-400 mr-2"></i>
                        <span class="text-xl font-bold">QuestionBank Pro</span>
                    </div>
                    <p class="text-gray-400">Streamlining question bank management for educational institutions
                        worldwide.</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition duration-300">Features</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Security</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">API</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition duration-300">Documentation</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Status</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 QuestionBank Pro. All rights reserved. Built with CodeIgniter 4.</p>
            </div>
        </div>
    </footer>
        <!-- Registration Modal -->
<div id="registrationModal" class="fixed inset-0 z-50 hidden flex items-center justify-center p-4 bg-black bg-opacity-50">
    <div class="w-full max-w-2xl bg-white rounded-xl shadow-lg overflow-hidden max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-indigo-800">Register Your School</h2>
            <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Content - iframe loading your existing registration page -->
        <div class="h-[80vh]">
            <iframe id="registrationIframe" src="<?= base_url('register') ?>" class="w-full h-full border-0"></iframe>
        </div>
    </div>
</div>

<!-- Cancel Registration Confirmation Modal -->
<div id="cancelRegistrationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto bg-red-100 rounded-full mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-2">Cancel Registration?</h3>
            <p class="text-gray-600 text-center mb-6">
                Are you sure you want to cancel your registration? All entered information will be lost and you'll need to start over.
            </p>
            <div class="flex space-x-3">
                <button id="confirmCancelRegistration" class="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition duration-200 flex items-center justify-center">
                    <i class="fas fa-check mr-2"></i>Yes, Cancel
                </button>
                <button id="keepRegistering" class="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition duration-200 flex items-center justify-center">
                    <i class="fas fa-arrow-left mr-2"></i>Keep Going
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Login Modal -->
<div id="loginModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h2 id="loginModalTitle" class="text-xl font-semibold text-gray-800">Login</h2>
            <button id="closeLoginModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                &times;
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- SuperAdmin Login Form -->
            <div id="superadminLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">SuperAdmin Access</h3>
                    <p class="text-gray-500 text-sm">System administration portal</p>
                </div>

                <form id="superadminForm" method="post" action="<?= site_url('auth/login') ?>" class="space-y-5">
                    <input type="hidden" name="role" value="superadmin">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" id="superadmin-email" placeholder="<EMAIL>" required autocomplete="off"
                               class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               style="background-image: none !important;">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" id="superadmin-password" placeholder="••••••••" required autocomplete="off"
                                   class="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <button type="button" onclick="togglePassword('superadmin-password')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 focus:outline-none">
                                <i id="superadmin-password-eye" class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="remember_me" id="superadmin-remember" value="1" class="rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" onclick="openForgotPasswordModal('school')" class="text-sm text-red-600 hover:text-red-500 hover:underline">Forgot password?</a>
                    </div>

                    <button type="submit"
                            class="w-full bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition duration-300">
                        Sign In as SuperAdmin
                    </button>
                </form>
            </div>

            <!-- SchoolAdmin Login Form -->
            <div id="schooladminLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">SchoolAdmin Access</h3>
                    <p class="text-gray-500 text-sm">Manage your school's content</p>
                </div>

                <form id="schooladminForm" method="post" action="<?= site_url('auth/login') ?>" class="space-y-5">
                    <input type="hidden" name="role" value="schooladmin">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" id="schooladmin-email" placeholder="<EMAIL>" required autocomplete="off"
                               class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               style="background-image: none !important;">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" id="schooladmin-password" placeholder="••••••••" required autocomplete="off"
                                   class="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <button type="button" onclick="togglePassword('schooladmin-password')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 focus:outline-none">
                                <i id="schooladmin-password-eye" class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="remember_me" id="schooladmin-remember" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" onclick="openForgotPasswordModal('school')" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">Forgot password?</a>
                    </div>

                    <button type="submit"
                            class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300">
                        Sign In as SchoolAdmin
                    </button>
                </form>
            </div>

            <!-- Staff Login Form -->
            <div id="staffLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">Staff Access</h3>
                    <p class="text-gray-500 text-sm">Access your question bank account</p>
                </div>

                <form id="staffForm" method="post" action="<?= site_url('staff/authenticate') ?>" class="space-y-5">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" id="staff-email" placeholder="<EMAIL>" required autocomplete="off"
                               class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               style="background-image: none !important;">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" id="staff-password" placeholder="••••••••" required autocomplete="off"
                                   class="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <button type="button" onclick="togglePassword('staff-password')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 focus:outline-none">
                                <i id="staff-password-eye" class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" name="remember_me" id="staff-remember" value="1" class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" onclick="openForgotPasswordModal('user')" class="text-sm text-green-600 hover:text-green-500 hover:underline">Forgot password?</a>
                    </div>

                    <button type="submit"
                            class="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition duration-300">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In as Staff
                    </button>
                </form>
            </div>

            <!-- Error/Success Messages -->
            <div id="loginMessages" class="mt-4"></div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div id="forgotPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h2 id="forgotPasswordModalTitle" class="text-xl font-semibold text-gray-800">
                <i class="fas fa-key mr-2 text-indigo-600"></i>Forgot Password
            </h2>
            <button id="closeForgotPasswordModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                &times;
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Success/Error Messages -->
            <div id="forgotPasswordMessages" class="hidden mb-4"></div>

            <!-- Instructions -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                    <div class="text-sm text-blue-700">
                        <p class="font-medium mb-1">Password Reset Instructions:</p>
                        <ul class="list-disc list-inside space-y-1 text-xs">
                            <li>Enter your registered email address</li>
                            <li>Check your email for the reset link</li>
                            <li>The link will expire in 1 hour</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Forgot Password Form -->
            <form id="forgotPasswordForm" class="space-y-5">
                <?= csrf_field() ?>
                <input type="hidden" id="forgotPasswordUserType" name="user_type" value="user">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="email" name="email" id="forgotPasswordEmail" required
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                               placeholder="Enter your registered email">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <button type="submit" id="forgotPasswordSubmitBtn"
                        class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Reset Link
                </button>
            </form>

            <!-- Back to Login -->
            <div class="mt-6 text-center">
                <button id="backToLoginBtn" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Login
                </button>
            </div>

            <!-- Security Notice -->
            <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-shield-alt text-gray-500 mt-1 mr-3"></i>
                    <div class="text-xs text-gray-600">
                        <p class="font-medium mb-1">Security Notice:</p>
                        <p>For your security, we'll only send reset instructions to registered email addresses.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-800">
                <i class="fas fa-key mr-2 text-indigo-600"></i>Reset Your Password
            </h2>
            <button id="closeResetPasswordModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                &times;
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Success/Error Messages -->
            <div id="resetPasswordMessages" class="hidden mb-4"></div>

            <!-- User Info -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-user-circle text-blue-600 mr-3 text-lg"></i>
                    <div>
                        <p class="text-sm text-gray-600">Resetting password for:</p>
                        <p class="font-medium text-gray-800" id="resetPasswordEmail"></p>
                    </div>
                </div>
            </div>

            <!-- Reset Password Form -->
            <form id="resetPasswordForm" class="space-y-5">
                <?= csrf_field() ?>
                <input type="hidden" id="resetPasswordToken" name="token" value="">
                <input type="hidden" id="resetPasswordUserType" name="user_type" value="">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        New Password <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="password" name="password" id="resetPasswordNewPassword" required
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                               placeholder="Enter new password"
                               minlength="8"
                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                               title="Password must contain at least 8 characters, including uppercase, lowercase, number and special character">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-4" onclick="togglePasswordVisibility('resetPasswordNewPassword', this)">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                        </button>
                    </div>

                    <!-- Password Requirements -->
                    <div class="mt-2 text-xs">
                        <div class="text-gray-600 font-medium mb-1">Password Requirements:</div>
                        <div class="space-y-1">
                            <div id="req-length" class="flex items-center text-gray-500">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <span>At least 8 characters</span>
                            </div>
                            <div id="req-uppercase" class="flex items-center text-gray-500">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <span>One uppercase letter (A-Z)</span>
                            </div>
                            <div id="req-lowercase" class="flex items-center text-gray-500">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <span>One lowercase letter (a-z)</span>
                            </div>
                            <div id="req-number" class="flex items-center text-gray-500">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <span>One number (0-9)</span>
                            </div>
                            <div id="req-special" class="flex items-center text-gray-500">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <span>One special character (@$!%*?&)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Confirm New Password <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="password" name="confirm_password" id="resetPasswordConfirmPassword" required
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                               placeholder="Confirm new password" minlength="8">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-4" onclick="togglePasswordVisibility('resetPasswordConfirmPassword', this)">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                        </button>
                    </div>

                    <!-- Password Match Indicator -->
                    <div id="password-match-indicator" class="mt-2 text-xs hidden">
                        <div class="flex items-center">
                            <i id="match-icon" class="fas fa-circle text-xs mr-2"></i>
                            <span id="match-text">Passwords match</span>
                        </div>
                    </div>
                </div>

                <button type="submit" id="resetPasswordSubmitBtn"
                        class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                    <i class="fas fa-key mr-2"></i>
                    Reset Password
                </button>
            </form>

            <!-- Back to Login -->
            <div class="mt-6 text-center">
                <button id="backToLoginFromResetBtn" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Login
                </button>
            </div>
        </div>
    </div>
</div>

    <script>
        // Check for logout message and show alert
        <?php if (session()->getFlashdata('logout_success')): ?>
        document.addEventListener('DOMContentLoaded', function() {
            alert('Logged out successfully!');
        });
        <?php endif; ?>

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        // Add scroll effect to navigation
        window.addEventListener('scroll', function () {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-white/95', 'backdrop-blur-sm');
            } else {
                nav.classList.remove('bg-white/95', 'backdrop-blur-sm');
            }
        });
        // Button click handlers
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function () {
                if (this.textContent.includes('Schedule Demo')) {
                    alert('Demo scheduling would open calendar booking system!');
                } else if (this.textContent.includes('Learn More')) {
                    document.querySelector('#features').scrollIntoView({ behavior: 'smooth' });
                }
            });
        });


        // Login dropdown functionality
        document.addEventListener('click', function (event) {
            const toggle = document.getElementById('loginToggle');
            const menu = document.getElementById('loginMenu');

            if (toggle.contains(event.target)) {
                menu.classList.toggle('hidden');
            } else {
                menu.classList.add('hidden');
            }
        });

        // Login Modal functionality
        const loginModal = document.getElementById('loginModal');
        const closeLoginModalBtn = document.getElementById('closeLoginModalBtn');
        const superadminForm = document.getElementById('superadminLoginForm');
        const schooladminForm = document.getElementById('schooladminLoginForm');
        const staffForm = document.getElementById('staffLoginForm');
        const loginModalTitle = document.getElementById('loginModalTitle');

        function openLoginModal(type) {
            // Hide dropdown menu
            document.getElementById('loginMenu').classList.add('hidden');

            // Show modal
            loginModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Hide all forms first
            superadminForm.classList.add('hidden');
            schooladminForm.classList.add('hidden');
            staffForm.classList.add('hidden');

            // Show appropriate form
            if (type === 'superadmin') {
                superadminForm.classList.remove('hidden');
                loginModalTitle.textContent = 'SuperAdmin Login';
            } else if (type === 'schooladmin') {
                schooladminForm.classList.remove('hidden');
                loginModalTitle.textContent = 'School Admin Login';
            } else if (type === 'staff') {
                staffForm.classList.remove('hidden');
                loginModalTitle.textContent = 'Staff Login';
            }
        }

        // Close login modal
        closeLoginModalBtn.addEventListener('click', function() {
            loginModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            // Clear any error messages
            document.getElementById('loginMessages').innerHTML = '';
        });

        // Close modal when clicking outside
        loginModal.addEventListener('click', function(e) {
            if (e.target === loginModal) {
                loginModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
                document.getElementById('loginMessages').innerHTML = '';
            }
        });

        // Handle login form submissions with AJAX
        document.getElementById('superadminForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLoginSubmit(this);
        });

        document.getElementById('schooladminForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLoginSubmit(this);
        });

        document.getElementById('staffForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleStaffLoginSubmit(this);
        });

        function handleLoginSubmit(form) {
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Signing in...';

            // Clear previous messages
            document.getElementById('loginMessages').innerHTML = '';

            fetch('<?= site_url('auth/login') ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Successful login - redirect to dashboard
                    showLoginMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    // Show error message
                    showLoginMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showLoginMessage('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        }

        function handleStaffLoginSubmit(form) {
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Signing in...';

            // Clear previous messages
            document.getElementById('loginMessages').innerHTML = '';

            fetch('<?= site_url('staff/authenticate') ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Successful login - redirect to staff dashboard
                    showLoginMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    // Show error message
                    showLoginMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showLoginMessage('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        }

        function showLoginMessage(message, type) {
            const messagesDiv = document.getElementById('loginMessages');
            const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
            messagesDiv.innerHTML = `
                <div class="p-3 rounded border ${alertClass}">
                    ${message}
                </div>
            `;
        }

        // Registration Modal functionality
        const modal = document.getElementById('registrationModal');
        const openModalBtns = document.querySelectorAll('[data-register-modal]');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const registrationIframe = document.getElementById('registrationIframe');

// Open modal when any register button is clicked
openModalBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        const selectedPlan = this.getAttribute('data-plan') || 'free';

        // Store selected plan in session storage
        sessionStorage.setItem('selectedPlan', selectedPlan);

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent scrolling

        // Set iframe source with plan parameter only if different
        const baseUrl = registrationIframe.src.split('?')[0];
        const newUrl = baseUrl + '?plan=' + selectedPlan;
        if (registrationIframe.src !== newUrl) {
            registrationIframe.src = newUrl;
        }
    });
});

// Close modal when X button is clicked - show confirmation
closeModalBtn.addEventListener('click', function() {
    showCancelConfirmation();
});

// Close modal when clicking outside the modal content
modal.addEventListener('click', function(e) {
    if (e.target === modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    }
});

// Listen for messages from the iframe
window.addEventListener('message', function(e) {
    if (e.data === 'registrationSuccess') {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    } else if (e.data && e.data.action === 'switchToLogin') {
        // Close registration modal
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling

        // Open login modal with specified type
        const loginType = e.data.type || 'schooladmin';
        openLoginModal(loginType);
    }
});


// Forgot Password Modal functionality
const forgotPasswordModal = document.getElementById('forgotPasswordModal');
const closeForgotPasswordModalBtn = document.getElementById('closeForgotPasswordModalBtn');
const forgotPasswordForm = document.getElementById('forgotPasswordForm');
const forgotPasswordMessages = document.getElementById('forgotPasswordMessages');
const backToLoginBtn = document.getElementById('backToLoginBtn');

function openForgotPasswordModal(userType) {
    // Hide login modal if open
    loginModal.classList.add('hidden');

    // Set user type
    document.getElementById('forgotPasswordUserType').value = userType;

    // Update modal title based on user type
    const modalTitle = document.getElementById('forgotPasswordModalTitle');
    if (userType === 'school') {
        modalTitle.innerHTML = '<i class="fas fa-key mr-2 text-indigo-600"></i>School Admin Password Reset';
    } else {
        modalTitle.innerHTML = '<i class="fas fa-key mr-2 text-indigo-600"></i>Staff Password Reset';
    }

    // Show modal
    forgotPasswordModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Clear form and messages
    forgotPasswordForm.reset();
    forgotPasswordMessages.classList.add('hidden');
    forgotPasswordMessages.innerHTML = '';

    // Focus on email input
    document.getElementById('forgotPasswordEmail').focus();
}

// Close forgot password modal
closeForgotPasswordModalBtn.addEventListener('click', function() {
    forgotPasswordModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
});

// Back to login button
backToLoginBtn.addEventListener('click', function() {
    forgotPasswordModal.classList.add('hidden');
    loginModal.classList.remove('hidden');
});

// Close modal when clicking outside
forgotPasswordModal.addEventListener('click', function(e) {
    if (e.target === forgotPasswordModal) {
        forgotPasswordModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
});

// Handle forgot password form submission
forgotPasswordForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = document.getElementById('forgotPasswordSubmitBtn');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

    // Clear previous messages
    forgotPasswordMessages.classList.add('hidden');

    fetch('<?= base_url('forgot-password/send') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showForgotPasswordMessage(data.message, 'success');
            forgotPasswordForm.reset();
        } else {
            showForgotPasswordMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showForgotPasswordMessage('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showForgotPasswordMessage(message, type) {
    const isSuccess = type === 'success';
    const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
    const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

    forgotPasswordMessages.innerHTML = `
        <div class="p-4 ${bgColor} border rounded-lg">
            <div class="flex items-center">
                <i class="${icon} mr-2"></i>
                ${message}
            </div>
        </div>
    `;
    forgotPasswordMessages.classList.remove('hidden');

    // Scroll to message
    forgotPasswordMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Reset Password Modal functionality
const resetPasswordModal = document.getElementById('resetPasswordModal');
const closeResetPasswordModalBtn = document.getElementById('closeResetPasswordModalBtn');
const resetPasswordForm = document.getElementById('resetPasswordForm');
const resetPasswordMessages = document.getElementById('resetPasswordMessages');
const backToLoginFromResetBtn = document.getElementById('backToLoginFromResetBtn');

// Close reset password modal
closeResetPasswordModalBtn.addEventListener('click', function() {
    resetPasswordModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
});

// Back to login button
backToLoginFromResetBtn.addEventListener('click', function() {
    resetPasswordModal.classList.add('hidden');
    loginModal.classList.remove('hidden');
});

// Close modal when clicking outside
resetPasswordModal.addEventListener('click', function(e) {
    if (e.target === resetPasswordModal) {
        resetPasswordModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
});

// Handle reset password form submission
resetPasswordForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const password = document.getElementById('resetPasswordNewPassword').value;
    const confirmPassword = document.getElementById('resetPasswordConfirmPassword').value;
    const submitBtn = document.getElementById('resetPasswordSubmitBtn');
    const originalText = submitBtn.innerHTML;

    // Validate password requirements
    const isPasswordValid = updatePasswordRequirements(password);
    const isPasswordMatch = updatePasswordMatch();

    if (!isPasswordValid) {
        showResetPasswordMessage('Please ensure your password meets all requirements.', 'error');
        return;
    }

    if (!isPasswordMatch) {
        showResetPasswordMessage('Passwords do not match. Please check and try again.', 'error');
        return;
    }

    const formData = new FormData(this);

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';

    // Clear previous messages
    resetPasswordMessages.classList.add('hidden');

    fetch('<?= base_url('forgot-password/reset') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Reset password response:', data);
        if (data.success) {
            showResetPasswordMessage(data.message, 'success');
            resetPasswordForm.reset();
            setTimeout(() => {
                resetPasswordModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
                // Open login modal
                openLoginModal('schooladmin');
            }, 2000);
        } else {
            showResetPasswordMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showResetPasswordMessage('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showResetPasswordMessage(message, type) {
    const isSuccess = type === 'success';
    const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
    const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

    resetPasswordMessages.innerHTML = `
        <div class="p-4 ${bgColor} border rounded-lg">
            <div class="flex items-center">
                <i class="${icon} mr-2"></i>
                <span>${message}</span>
            </div>
        </div>
    `;
    resetPasswordMessages.classList.remove('hidden');

    // Scroll to message
    resetPasswordMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Check for reset token in URL parameters and open reset password modal
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('reset_token');
    const email = urlParams.get('email');
    const userType = urlParams.get('user_type');
    const error = urlParams.get('error');

    if (error === 'invalid_token') {
        showForgotPasswordMessage('Invalid or expired password reset link. Please request a new one.', 'error');
        openForgotPasswordModal('school');
    } else if (resetToken && email) {
        openResetPasswordModal(resetToken, email, userType);
    }
});

function openResetPasswordModal(token, email, userType) {
    // Hide any open modals
    document.getElementById('loginModal').classList.add('hidden');
    document.getElementById('forgotPasswordModal').classList.add('hidden');

    // Show reset password modal
    document.getElementById('resetPasswordModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Set form data
    document.getElementById('resetPasswordToken').value = token;
    document.getElementById('resetPasswordEmail').textContent = email;
    document.getElementById('resetPasswordUserType').value = userType;

    // Clear form and messages
    document.getElementById('resetPasswordForm').reset();
    document.getElementById('resetPasswordMessages').classList.add('hidden');
    document.getElementById('resetPasswordMessages').innerHTML = '';

    // Focus on password input
    document.getElementById('resetPasswordNewPassword').focus();

    // Clean URL
    window.history.replaceState({}, document.title, window.location.pathname);
}

// Password visibility toggle function
function togglePasswordVisibility(inputId, button) {
    const input = document.getElementById(inputId);
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password validation functions
function validatePasswordRequirements(password) {
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[@$!%*?&]/.test(password)
    };

    return requirements;
}

function updatePasswordRequirements(password) {
    const requirements = validatePasswordRequirements(password);

    // Update each requirement indicator
    Object.keys(requirements).forEach(req => {
        const element = document.getElementById(`req-${req}`);
        const icon = element.querySelector('i');
        const text = element.querySelector('span');

        if (requirements[req]) {
            element.classList.remove('text-gray-500');
            element.classList.add('text-green-600');
            icon.classList.remove('fa-circle');
            icon.classList.add('fa-check-circle');
        } else {
            element.classList.remove('text-green-600');
            element.classList.add('text-gray-500');
            icon.classList.remove('fa-check-circle');
            icon.classList.add('fa-circle');
        }
    });

    return Object.values(requirements).every(req => req);
}

function updatePasswordMatch() {
    const password = document.getElementById('resetPasswordNewPassword').value;
    const confirmPassword = document.getElementById('resetPasswordConfirmPassword').value;
    const indicator = document.getElementById('password-match-indicator');
    const icon = document.getElementById('match-icon');
    const text = document.getElementById('match-text');

    if (confirmPassword.length === 0) {
        indicator.classList.add('hidden');
        return false;
    }

    indicator.classList.remove('hidden');

    if (password === confirmPassword) {
        indicator.classList.remove('text-red-600');
        indicator.classList.add('text-green-600');
        icon.classList.remove('fa-times-circle');
        icon.classList.add('fa-check-circle');
        text.textContent = 'Passwords match';
        return true;
    } else {
        indicator.classList.remove('text-green-600');
        indicator.classList.add('text-red-600');
        icon.classList.remove('fa-check-circle');
        icon.classList.add('fa-times-circle');
        text.textContent = 'Passwords do not match';
        return false;
    }
}

// Add event listeners for real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('resetPasswordNewPassword');
    const confirmPasswordInput = document.getElementById('resetPasswordConfirmPassword');

    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            updatePasswordRequirements(this.value);
            updatePasswordMatch();
        });
    }

    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            updatePasswordMatch();
        });
    }
});

// Cancel Registration Confirmation functionality
function showCancelConfirmation() {
    const cancelModal = document.getElementById('cancelRegistrationModal');
    cancelModal.classList.remove('hidden');
    cancelModal.querySelector('.bg-white').classList.remove('scale-95');
    cancelModal.querySelector('.bg-white').classList.add('scale-100');
}

function hideCancelConfirmation() {
    const cancelModal = document.getElementById('cancelRegistrationModal');
    cancelModal.querySelector('.bg-white').classList.remove('scale-100');
    cancelModal.querySelector('.bg-white').classList.add('scale-95');
    setTimeout(() => {
        cancelModal.classList.add('hidden');
    }, 200);
}

// Setup cancel confirmation event listeners
document.getElementById('keepRegistering').addEventListener('click', function() {
    hideCancelConfirmation();
});

document.getElementById('confirmCancelRegistration').addEventListener('click', function() {
    // Send message to iframe to reset the form
    const iframe = document.getElementById('registrationIframe');
    iframe.contentWindow.postMessage('resetRegistrationForm', '*');

    // Close confirmation modal
    hideCancelConfirmation();

    // Close registration modal
    modal.classList.add('hidden');
    document.body.style.overflow = '';

    // Show success message
    showSuccessMessage('Registration cancelled. You can start fresh anytime!');
});

// Close cancel modal when clicking outside
document.getElementById('cancelRegistrationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCancelConfirmation();
    }
});

// Show success message function
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    successDiv.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        successDiv.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        successDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 300);
    }, 3000);
}

// Password toggle functionality
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const eyeIcon = document.getElementById(fieldId + '-eye');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Auto-clear form fields on page load to prevent browser auto-fill
document.addEventListener('DOMContentLoaded', function() {
    // Clear form fields immediately
    clearAllLoginForms();

    // Also clear after a short delay to ensure browser auto-fill is overridden
    setTimeout(clearAllLoginForms, 100);
    setTimeout(clearAllLoginForms, 500);
});

// Function to clear all login forms
function clearAllLoginForms() {
    // SuperAdmin form
    const superadminEmail = document.getElementById('superadmin-email');
    const superadminPassword = document.getElementById('superadmin-password');

    // SchoolAdmin form
    const schooladminEmail = document.getElementById('schooladmin-email');
    const schooladminPassword = document.getElementById('schooladmin-password');

    // Staff form
    const staffEmail = document.getElementById('staff-email');
    const staffPassword = document.getElementById('staff-password');

    // Clear all fields
    if (superadminEmail) superadminEmail.value = '';
    if (superadminPassword) superadminPassword.value = '';
    if (schooladminEmail) schooladminEmail.value = '';
    if (schooladminPassword) schooladminPassword.value = '';
    if (staffEmail) staffEmail.value = '';
    if (staffPassword) staffPassword.value = '';
}

// Clear forms when page becomes visible (handles browser back/forward)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        setTimeout(clearAllLoginForms, 100);
    }
});

// Clear forms when window gains focus
window.addEventListener('focus', function() {
    setTimeout(clearAllLoginForms, 100);
});

// Clear forms when login modal is opened
function openLoginModal(type) {
    const modal = document.getElementById('loginModal');
    const superadminForm = document.getElementById('superadminLoginForm');
    const schooladminForm = document.getElementById('schooladminLoginForm');
    const staffForm = document.getElementById('staffLoginForm');
    const title = document.getElementById('loginModalTitle');

    // Hide all forms first
    superadminForm.classList.add('hidden');
    schooladminForm.classList.add('hidden');
    staffForm.classList.add('hidden');

    // Clear all forms when modal opens
    setTimeout(clearAllLoginForms, 50);

    // Show the appropriate form
    if (type === 'superadmin') {
        superadminForm.classList.remove('hidden');
        title.textContent = 'SuperAdmin Login';
    } else if (type === 'schooladmin') {
        schooladminForm.classList.remove('hidden');
        title.textContent = 'SchoolAdmin Login';
    } else if (type === 'staff') {
        staffForm.classList.remove('hidden');
        title.textContent = 'Staff Login';
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

    </script>
</body>
</html>