<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($school_name) ? esc($school_name) : 'School Question Bank' ?> - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stats-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card-2 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-card-3 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stats-card-4 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .nav-item { transition: all 0.3s ease; }
        .nav-item:hover { background: linear-gradient(90deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%); }
        .nav-item.active { background: linear-gradient(90deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.05) 100%); border-right: 4px solid #6366f1; }
        .section { display: none; }
        .section.active { display: block; }
        .modal-backdrop { backdrop-filter: blur(4px); }
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .notification-badge { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }
        .settings-tab-content { max-width: 100%; overflow-x: hidden; }
        #settings-section { max-width: 100%; overflow-x: hidden; }
        .settings-tab {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .settings-tab:hover {
            background-color: rgba(99, 102, 241, 0.05);
            border-radius: 6px;
        }
        .settings-tab.active {
            background-color: rgba(99, 102, 241, 0.1);
            border-radius: 6px;
        }
        .readonly-field {
            background-color: #f9fafb !important;
            border-color: #d1d5db !important;
            color: #6b7280 !important;
            cursor: not-allowed !important;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Enhanced Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl sidebar-transition flex flex-col" id="sidebar">
        <!-- School Header -->
        <div class="gradient-bg h-20 flex items-center justify-center relative overflow-hidden flex-shrink-0">
            <div class="absolute inset-0 bg-black opacity-10"></div>
            <div class="relative text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-1">
                    <i class="fas fa-graduation-cap text-white text-xl"></i>
                </div>
                <h1 class="text-white text-sm font-bold truncate px-2"><?= isset($school_name) ? esc($school_name) : 'School Admin' ?></h1>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="mt-6 px-3 flex-grow overflow-y-auto">
            <div class="space-y-1">
                <a href="#" class="nav-item active flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium" onclick="showSection('dashboard', this)">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tachometer-alt text-indigo-600 text-sm"></i>
                    </div>
                    <span>Dashboard</span>
                </a>

                <a href="#" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium" onclick="showSection('questions', this)">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-clipboard-question text-yellow-600 text-sm"></i>
                    </div>
                    <span>Question Review</span>
                    <span id="pending-count" class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full notification-badge hidden">0</span>
                </a>

                <a href="<?= site_url('schooladmin/question-papers') ?>" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-file-alt text-blue-600 text-sm"></i>
                    </div>
                    <span>Question Papers</span>
                </a>

                <a href="#" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium" onclick="showSection('staff', this)">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chalkboard-teacher text-green-600 text-sm"></i>
                    </div>
                    <span>Staff Management</span>
                </a>

                <a href="#" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium" onclick="showSection('subscription', this)">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-credit-card text-orange-600 text-sm"></i>
                    </div>
                    <span>Subscription.</span>
                </a>

                <a href="<?= site_url('schooladmin/settings') ?>" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cog text-purple-600 text-sm"></i>
                    </div>
                    <span>Settings</span>
                </a>
            </div>
        </nav>

        <!-- Logout Button at Bottom -->
        <div class="px-3 pb-4">
            <a href="<?= site_url('logout') ?>" class="nav-item flex items-center px-4 py-3 text-red-600 rounded-lg font-medium hover:bg-red-50 transition-colors">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-sign-out-alt text-red-600 text-sm"></i>
                </div>
                <span>Logout</span>
            </a>
        </div>

        <!-- User Profile Section -->
        <div class="p-4 border-t border-gray-200 flex-shrink-0">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">A</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">Admin User</p>
                    <p class="text-xs text-gray-500 truncate">School Administrator</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Enhanced Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100" onclick="toggleSidebar()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="ml-4">
                        <h2 id="page-title" class="text-2xl font-bold text-gray-800">Dashboard</h2>
                        <p id="page-subtitle" class="text-sm text-gray-600 mt-1">Welcome back, manage your school efficiently</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Quick Actions -->
                    <div class="hidden md:flex items-center space-x-2">
                        <button onclick="showSection('questions')" class="bg-indigo-50 text-indigo-600 px-3 py-2 rounded-lg text-sm font-medium hover:bg-indigo-100 transition-colors">
                            <i class="fas fa-clipboard-question mr-1"></i>
                            Review Questions
                        </button>

                    </div>

                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notification-btn" class="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors" onclick="toggleNotifications()">
                            <i class="fas fa-bell text-xl"></i>
                            <span id="notification-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center notification-badge">3</span>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 modal-backdrop">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">Notifications</h3>
                            </div>
                            <div id="notifications-list" class="max-h-96 overflow-y-auto">
                                <div class="p-3 border-b border-gray-100 hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-800 font-medium">New question submitted for review</p>
                                            <p class="text-xs text-gray-500 mt-1">Mathematics - Class 10 • 2 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 border-b border-gray-100 hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-800 font-medium">Question approved successfully</p>
                                            <p class="text-xs text-gray-500 mt-1">Physics - Chapter 5 • 1 hour ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 border-b border-gray-100 hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-800 font-medium">New staff member registered</p>
                                            <p class="text-xs text-gray-500 mt-1">John Doe - English Teacher • 3 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-3 border-t border-gray-200 text-center">
                                <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">View all notifications</button>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center space-x-3 hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors" onclick="toggleUserMenu()">
                            <div class="w-8 h-8 gradient-bg rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">A</span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-gray-700 font-medium text-sm"><?= isset($school_name) ? esc($school_name) : 'School' ?> Admin</p>
                                <p class="text-gray-500 text-xs">Administrator</p>
                            </div>
                            <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                        </button>

                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-200">
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">
                                <i class="fas fa-user mr-3 text-gray-400"></i>Profile Settings
                            </a>

                            <a href="<?= site_url('schooladmin/settings') ?>" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">
                                <i class="fas fa-cog mr-3 text-gray-400"></i>School Settings
                            </a>

                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">
                                <i class="fas fa-question-circle mr-3 text-gray-400"></i>Help & Support
                            </a>
                            <hr class="my-2">
                            <a href="<?= site_url('logout') ?>" class="block px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                <i class="fas fa-sign-out-alt mr-3"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Enhanced Dashboard Section -->
            <div id="dashboard-section" class="section active animate-fade-in">
                <!-- Welcome Banner -->
                <div class="gradient-bg rounded-xl p-6 mb-8 text-white relative overflow-hidden">
                    <div class="absolute inset-0 bg-black opacity-10"></div>
                    <div class="relative">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold mb-2">Welcome back, Admin!</h1>
                                <p class="text-white text-opacity-90">Here's what's happening at <?= isset($school_name) ? esc($school_name) : 'your school' ?> today</p>
                            </div>
                            <div class="hidden md:block">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-school text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Total Questions Card -->
                    <div class="stats-card rounded-xl p-6 text-white card-hover relative overflow-hidden cursor-pointer transition-transform hover:scale-105" onclick="navigateToQuestions()">
                        <div class="absolute inset-0 bg-black opacity-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm font-medium">Total Questions</p>
                                    <p class="text-3xl font-bold mt-2"><?= isset($stats['total_questions']) ? number_format($stats['total_questions']) : '0' ?></p>
                                    <div class="flex items-center mt-3">
                                        <div class="w-2 h-2 bg-white rounded-full mr-2"></div>
                                        <p class="text-white text-opacity-90 text-sm">Question Bank</p>
                                    </div>
                                </div>
                                <div class="w-14 h-14 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-question-circle text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pending Review Card -->
                    <div class="stats-card-2 rounded-xl p-6 text-white card-hover relative overflow-hidden cursor-pointer transition-transform hover:scale-105" onclick="navigateToPendingReview()">
                        <div class="absolute inset-0 bg-black opacity-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm font-medium">Pending Review</p>
                                    <p class="text-3xl font-bold mt-2"><?= isset($stats['pending_review']) ? $stats['pending_review'] : '0' ?></p>
                                    <div class="flex items-center mt-3">
                                        <div class="w-2 h-2 bg-white rounded-full mr-2"></div>
                                        <p class="text-white text-opacity-90 text-sm"><?= (isset($stats['pending_review']) && $stats['pending_review'] > 0) ? 'Needs attention' : 'All clear' ?></p>
                                    </div>
                                </div>
                                <div class="w-14 h-14 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Staff Card -->
                    <div class="stats-card-3 rounded-xl p-6 text-white card-hover relative overflow-hidden cursor-pointer transition-transform hover:scale-105" onclick="navigateToStaff()">
                        <div class="absolute inset-0 bg-black opacity-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm font-medium">Active Staff</p>
                                    <p class="text-3xl font-bold mt-2"><?= isset($stats['active_users']) ? number_format($stats['active_users']) : '1' ?></p>
                                    <div class="flex items-center mt-3">
                                        <div class="w-2 h-2 bg-white rounded-full mr-2"></div>
                                        <p class="text-white text-opacity-90 text-sm">Teaching Team</p>
                                    </div>
                                </div>
                                <div class="w-14 h-14 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Success Rate Card -->
                    <div class="stats-card-4 rounded-xl p-6 text-white card-hover relative overflow-hidden cursor-pointer transition-transform hover:scale-105" onclick="navigateToApprovalStats()">
                        <div class="absolute inset-0 bg-black opacity-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm font-medium">Approval Rate</p>
                                    <p class="text-3xl font-bold mt-2"><?= isset($stats['success_rate']) ? $stats['success_rate'] . '%' : '0%' ?></p>
                                    <div class="flex items-center mt-3">
                                        <div class="w-2 h-2 bg-white rounded-full mr-2"></div>
                                        <p class="text-white text-opacity-90 text-sm">Quality Score</p>
                                    </div>
                                </div>
                                <div class="w-14 h-14 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-trophy text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Analytics Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button onclick="showSection('questions')" class="w-full flex items-center p-3 bg-indigo-50 text-indigo-700 rounded-lg hover:bg-indigo-100 transition-colors">
                                <i class="fas fa-clipboard-question mr-3"></i>
                                <span class="font-medium">Review Questions</span>
                                <span id="quick-pending-count" class="ml-auto bg-indigo-200 text-indigo-800 px-2 py-1 rounded-full text-xs"><?= isset($stats['pending_review']) ? $stats['pending_review'] : '0' ?></span>
                            </button>

                            <button onclick="showSection('staff')" class="w-full flex items-center p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
                                <i class="fas fa-user-plus mr-3"></i>
                                <span class="font-medium">Add Staff Member</span>
                            </button>

                        </div>
                    </div>

                    <!-- Question Statistics Chart -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Question Status Overview</h3>
                        <div class="relative h-48">
                            <canvas id="questionStatusChart"></canvas>
                        </div>
                        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="text-2xl font-bold text-green-600"><?= isset($stats['approved_questions']) ? $stats['approved_questions'] : '0' ?></p>
                                <p class="text-sm text-gray-600">Approved</p>
                            </div>
                            <div>
                                <p class="text-2xl font-bold text-yellow-600"><?= isset($stats['pending_review']) ? $stats['pending_review'] : '0' ?></p>
                                <p class="text-sm text-gray-600">Pending</p>
                            </div>
                            <div>
                                <p class="text-2xl font-bold text-red-600"><?= isset($stats['rejected_questions']) ? $stats['rejected_questions'] : '0' ?></p>
                                <p class="text-sm text-gray-600">Rejected</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                        <div class="space-y-4" id="recent-activity">
                            <?php if (!empty($recent_activity)): ?>
                                <?php
                                // Show only first 8 activities in the dashboard widget
                                $dashboardActivities = array_slice($recent_activity, 0, 8);
                                foreach ($dashboardActivities as $activity): ?>
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-<?= $activity['color'] ?>-100 rounded-full flex items-center justify-center">
                                            <i class="<?= $activity['icon'] ?> text-<?= $activity['color'] ?>-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900 font-medium"><?= esc($activity['title']) ?></p>
                                            <p class="text-xs text-gray-500"><?= esc($activity['description']) ?> • <?php
                                                $time = time() - strtotime($activity['time']);
                                                if ($time < 60) echo 'just now';
                                                elseif ($time < 3600) echo floor($time/60) . ' min ago';
                                                elseif ($time < 86400) echo floor($time/3600) . ' hour' . (floor($time/3600) > 1 ? 's' : '') . ' ago';
                                                elseif ($time < 2592000) echo floor($time/86400) . ' day' . (floor($time/86400) > 1 ? 's' : '') . ' ago';
                                                elseif ($time < 31536000) echo floor($time/2592000) . ' month' . (floor($time/2592000) > 1 ? 's' : '') . ' ago';
                                                else echo floor($time/31536000) . ' year' . (floor($time/31536000) > 1 ? 's' : '') . ' ago';
                                            ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-clock text-gray-300 text-3xl mb-3"></i>
                                    <p class="text-gray-500 text-sm">No recent activity</p>
                                    <p class="text-gray-400 text-xs">Activity will appear here as questions are submitted and reviewed</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($recent_activity)): ?>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <button onclick="showAllActivityModal()" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">View all activity</button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Subject Performance Overview -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Subject Performance</h3>
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <option>Last 7 days</option>
                            <option>Last 30 days</option>
                            <option>Last 3 months</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php if (!empty($subject_performance)): ?>
                            <?php foreach ($subject_performance as $subject): ?>
                                <div class="p-4 bg-gradient-to-r from-<?= $subject['color'] ?>-50 to-<?= $subject['color'] ?>-100 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-<?= $subject['color'] ?>-700"><?= esc($subject['name']) ?></p>
                                            <p class="text-2xl font-bold text-<?= $subject['color'] ?>-900"><?= $subject['total_questions'] ?></p>
                                            <p class="text-xs text-<?= $subject['color'] ?>-600">Questions</p>
                                            <div class="mt-1 flex space-x-2 text-xs">
                                                <span class="text-green-600"><?= $subject['approved_questions'] ?> approved</span>
                                                <span class="text-yellow-600"><?= $subject['pending_questions'] ?> pending</span>
                                            </div>
                                        </div>
                                        <div class="w-10 h-10 bg-<?= $subject['color'] ?>-200 rounded-full flex items-center justify-center">
                                            <i class="<?= $subject['icon'] ?> text-<?= $subject['color'] ?>-700"></i>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-span-full text-center py-8">
                                <i class="fas fa-book text-gray-300 text-3xl mb-3"></i>
                                <p class="text-gray-500 text-sm">No subjects with questions yet</p>
                                <p class="text-gray-400 text-xs">Subject performance will appear here as questions are created</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Enhanced Questions Section -->
            <div id="questions-section" class="section hidden animate-fade-in">
                <!-- Question Review Header -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Question Review Center</h3>
                                <p class="text-sm text-gray-600 mt-1">Review and manage questions submitted by staff</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex space-x-3">
                                <select id="review-status-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="loadQuestionsForReview(1)">
                                    <option value="all" selected>All Questions</option>
                                    <option value="pending">Pending Review</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                                <button onclick="loadQuestionsForReview(1)" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                                </button>
                                <button onclick="exportSelectedQuestions()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors" id="export-btn" disabled>
                                    <i class="fas fa-download mr-2"></i>Export Selected
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Review Statistics -->
                    <div class="px-6 py-4 bg-gray-50">
                        <div class="grid grid-cols-1 sm:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                                    <span class="text-2xl font-bold text-yellow-600" id="pending-stats">0</span>
                                </div>
                                <p class="text-sm font-medium text-gray-700">Pending</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                                    <span class="text-2xl font-bold text-green-600" id="approved-stats">0</span>
                                </div>
                                <p class="text-sm font-medium text-gray-700">Approved</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                                    <span class="text-2xl font-bold text-red-600" id="rejected-stats">0</span>
                                </div>
                                <p class="text-sm font-medium text-gray-700">Rejected</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                                    <span class="text-2xl font-bold text-blue-600" id="total-stats">0</span>
                                </div>
                                <p class="text-sm font-medium text-gray-700">Total</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Filters -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Search Questions</label>
                            <input type="text" id="question-search" placeholder="Search by question text..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onkeyup="debounceSearch()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                            <select id="subject-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="loadQuestionsForReview(1)">
                                <option value="">All Subjects</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                                <option value="Biology">Biology</option>
                                <option value="Tamil">Tamil</option>
                                <option value="English">English</option>
                                <option value="Science">Science</option>
                                <option value="Social Science">Social Science</option>
                                <option value="History">History</option>
                                <option value="Geography">Geography</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Standard</label>
                            <select id="standard-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="loadQuestionsForReview(1)">
                                <option value="">All Standards</option>
                                <option value="1">Class 1</option>
                                <option value="2">Class 2</option>
                                <option value="3">Class 3</option>
                                <option value="4">Class 4</option>
                                <option value="5">Class 5</option>
                                <option value="6">Class 6</option>
                                <option value="7">Class 7</option>
                                <option value="8">Class 8</option>
                                <option value="9">Class 9</option>
                                <option value="10">Class 10</option>
                                <option value="11">Class 11</option>
                                <option value="12">Class 12</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Type</label>
                            <select id="type-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="loadQuestionsForReview(1)">
                                <option value="">All Types</option>
                                <option value="multiple_choice">Multiple Choice</option>
                                <option value="short_answer">Short Answer</option>
                                <option value="long_answer">Long Answer</option>
                                <option value="essay">Essay</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Staff</label>
                            <select id="staff-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="loadQuestionsForReview(1)">
                                <option value="">All Staff</option>
                                <!-- Staff options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button onclick="clearFilters()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-times mr-2"></i>Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Questions Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">
                                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    </th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Question</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Staff</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Subject</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Standard</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Type</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Marks</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Status</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Submitted</th>
                                    <th class="text-left py-4 px-6 font-semibold text-gray-700">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="questions-review-table" class="divide-y divide-gray-200">
                                <tr>
                                    <td colspan="10" class="text-center py-12 text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-spinner fa-spin text-3xl mb-4 text-indigo-500"></i>
                                            <p class="text-lg font-medium">Loading questions...</p>
                                            <p class="text-sm">Please wait while we fetch the latest data</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600" id="pagination-info">Showing 0 to 0 of 0 questions</span>
                                <select id="per-page" class="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="changePerPage()">
                                    <option value="10">10 per page</option>
                                    <option value="25">25 per page</option>
                                    <option value="50">50 per page</option>
                                    <option value="100">100 per page</option>
                                </select>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <nav class="flex items-center space-x-2" id="pagination-controls">
                                    <button class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        <i class="fas fa-chevron-left mr-1"></i>Previous
                                    </button>
                                    <button class="px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">1</button>
                                    <button class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        Next<i class="fas fa-chevron-right ml-1"></i>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div id="bulk-actions" class="hidden px-6 py-3 bg-indigo-50 border-t border-indigo-200">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-indigo-700">
                                <span id="selected-count">0</span> questions selected
                            </span>
                            <div class="flex space-x-2">
                                <button onclick="bulkApprove()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-check mr-2"></i>Approve Selected
                                </button>
                                <button onclick="bulkReject()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-times mr-2"></i>Reject Selected
                                </button>
                                <button onclick="clearSelection()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Enhanced Staff Management Section -->
            <div id="staff-section" class="section hidden animate-fade-in">
                <!-- Staff Management Header -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Staff Management</h3>
                                <p class="text-sm text-gray-600 mt-1">Manage teaching staff, assignments, and performance</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <button onclick="showUserModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                    <i class="fas fa-user-plus mr-2"></i>Add Staff
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Staff Statistics -->
                    <div class="px-6 py-4 bg-gray-50">
                        <div class="grid grid-cols-1 sm:grid-cols-4 gap-4">
                            <div class="text-center">
                                <p id="total-staff-count" class="text-2xl font-bold text-blue-600">0</p>
                                <p class="text-sm text-gray-600">Total Staff</p>
                            </div>
                            <div class="text-center">
                                <p id="active-staff-count" class="text-2xl font-bold text-green-600">0</p>
                                <p class="text-sm text-gray-600">Active</p>
                            </div>
                            <div class="text-center">
                                <p id="inactive-staff-count" class="text-2xl font-bold text-red-600">0</p>
                                <p class="text-sm text-gray-600">Inactive</p>
                            </div>
                            <div class="text-center">
                                <p id="teachers-count" class="text-2xl font-bold text-purple-600">0</p>
                                <p class="text-sm text-gray-600">Teachers</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staff Filters -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <!-- Search Bar -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Staff</label>
                        <div class="relative">
                            <input type="text" id="staff-search" placeholder="Search by name, email, or designation..."
                                   class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                   oninput="filterStaff()">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                            <select id="designation-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="filterStaff()">
                                <option value="">All Designations</option>
                                <option value="Principal">Principal</option>
                                <option value="Vice Principal">Vice Principal</option>
                                <option value="HOD">HOD</option>
                                <option value="Teacher">Teacher</option>
                                <option value="Assistant Teacher">Assistant Teacher</option>
                                <option value="Admin Staff">Admin Staff</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="status-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" onchange="filterStaff()">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="on-leave">On Leave</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Actions</label>
                            <button onclick="clearStaffFilters()" class="w-full px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-times mr-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Staff Directory -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-800">Staff Directory</h4>
                    </div>

                    <!-- Staff Cards View -->
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="staff-cards-container">
                            <!-- Staff cards will be loaded dynamically -->
                            <div class="col-span-full text-center py-12">
                                <i class="fas fa-spinner fa-spin text-3xl text-indigo-500 mb-4"></i>
                                <p class="text-lg font-medium text-gray-700">Loading staff...</p>
                                <p class="text-sm text-gray-500">Please wait while we fetch staff data</p>
                            </div>
                        </div>
                    </div>


                </div>
            </div>






<!-- User Modal (for adding new users) -->
<div id="userModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">Register New User</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="hideUserModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <form id="addUserForm" onsubmit="submitUserForm(event)">
                <div class="space-y-4">
                    <div>
                        <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <input type="text" id="fullName" name="name" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="John Doe">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input type="password" id="password" name="password" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="••••••••">
                    </div>
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="••••••••">
                    </div>
                    <div>
                        <label for="designation" class="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                        <select id="designation" name="designation" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Designation</option>
                            <option value="Principal">Principal</option>
                            <option value="Vice Principal">Vice Principal</option>
                            <option value="HOD">HOD</option>
                            <option value="Teacher">Teacher</option>
                            <option value="Assistant Teacher">Assistant Teacher</option>
                            <option value="Admin Staff">Admin Staff</option>
                        </select>
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" id="phone" name="phone"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="1234567890">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="isActive" name="isActive" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="isActive" class="ml-2 block text-sm text-gray-700">Active User</label>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="hideUserModal()" 
                            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Add User
                    </button>
                </div>
            </form>
        </div>
    </div>
            </div>

            <!-- Subscription Section -->
            <div id="subscription-section" class="section hidden animate-fade-in">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Subscription Management</h2>
                                <p class="text-gray-600 text-sm mt-1">Manage your school's subscription plan and billing</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">Active</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Current Plan -->
                        <div class="grid md:grid-cols-2 gap-6 mb-8">
                            <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 border border-indigo-200">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Current Plan</h3>
                                    <i class="fas fa-crown text-indigo-600 text-xl"></i>
                                </div>
                                <div class="text-2xl font-bold text-indigo-600 mb-2">
                                    <?= isset($current_subscription) ? $current_subscription['plan_display_name'] : 'Free Trial' ?>
                                </div>
                                <div class="text-gray-600 mb-4">
                                    <?php if (isset($current_subscription)): ?>
                                        ₹<?= number_format($current_subscription['amount']) ?>/<?= $current_subscription['billing_cycle'] ?>
                                    <?php else: ?>
                                        Free for 14 days
                                    <?php endif; ?>
                                </div>
                                <div class="space-y-2 text-sm">
                                    <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>Unlimited questions</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>Up to 50 users</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>Priority support</span>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>Up to 50 questions</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>5 subjects maximum</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span>Email support</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">Usage Statistics</h3>
                                <div class="space-y-4">
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span>Questions Created</span>
                                            <span>
                                                <?= isset($stats['total_questions']) ? $stats['total_questions'] : 0 ?> /
                                                <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                                    Unlimited
                                                <?php else: ?>
                                                    50
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <?php
                                            $questionsUsed = isset($stats['total_questions']) ? $stats['total_questions'] : 0;
                                            $questionsLimit = (isset($current_subscription) && $current_subscription['plan_name'] === 'professional') ? 100 : 50; // Use 100 as visual limit for unlimited
                                            $questionsPercent = $questionsLimit > 0 ? min(($questionsUsed / $questionsLimit) * 100, 100) : 0;
                                            ?>
                                            <div class="bg-green-500 h-2 rounded-full" style="width: <?= $questionsPercent ?>%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span>Active Users</span>
                                            <span>
                                                <?= isset($stats['total_users']) ? $stats['total_users'] : 0 ?> /
                                                <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                                    50
                                                <?php else: ?>
                                                    5
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <?php
                                            $usersUsed = isset($stats['total_users']) ? $stats['total_users'] : 0;
                                            $usersLimit = (isset($current_subscription) && $current_subscription['plan_name'] === 'professional') ? 50 : 5;
                                            $usersPercent = $usersLimit > 0 ? min(($usersUsed / $usersLimit) * 100, 100) : 0;
                                            ?>
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: <?= $usersPercent ?>%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span>Subjects</span>
                                            <span>
                                                <?= isset($stats['total_subjects']) ? $stats['total_subjects'] : 0 ?> /
                                                <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                                    Unlimited
                                                <?php else: ?>
                                                    5
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <?php
                                            $subjectsUsed = isset($stats['total_subjects']) ? $stats['total_subjects'] : 0;
                                            $subjectsLimit = (isset($current_subscription) && $current_subscription['plan_name'] === 'professional') ? 20 : 5; // Use 20 as visual limit for unlimited
                                            $subjectsPercent = $subjectsLimit > 0 ? min(($subjectsUsed / $subjectsLimit) * 100, 100) : 0;
                                            ?>
                                            <div class="bg-purple-500 h-2 rounded-full" style="width: <?= $subjectsPercent ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Comparison -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Available Plans</h3>
                            <div class="grid md:grid-cols-2 gap-6">
                                <!-- Free Trial -->
                                <div class="border <?= (!isset($current_subscription) || $current_subscription['plan_name'] === 'free') ? 'border-2 border-blue-500 bg-blue-50' : 'border-gray-200' ?> rounded-lg p-6 <?= (!isset($current_subscription) || $current_subscription['plan_name'] === 'free') ? 'relative' : '' ?>">
                                    <?php if (!isset($current_subscription) || $current_subscription['plan_name'] === 'free'): ?>
                                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold">Current</span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="text-center">
                                        <h4 class="text-lg font-semibold mb-2">Free Trial</h4>
                                        <div class="text-2xl font-bold text-gray-800 mb-2">₹0</div>
                                        <p class="text-gray-600 mb-4">14 days free trial</p>
                                        <ul class="text-left space-y-2 mb-6 text-sm">
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Up to 50 questions</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>5 subjects maximum</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>5 users maximum</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Email support</li>
                                        </ul>
                                        <?php if (!isset($current_subscription) || $current_subscription['plan_name'] === 'free'): ?>
                                            <button class="w-full bg-gray-400 text-white py-2 rounded-lg cursor-not-allowed" disabled>
                                                Current Plan
                                            </button>
                                        <?php else: ?>
                                            <button class="w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition" onclick="alert('You cannot downgrade to Free Trial')">
                                                Downgrade
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Professional -->
                                <div class="border <?= (isset($current_subscription) && $current_subscription['plan_name'] === 'professional') ? 'border-2 border-indigo-500 bg-indigo-50' : 'border-gray-200' ?> rounded-lg p-6 <?= (isset($current_subscription) && $current_subscription['plan_name'] === 'professional') ? 'relative' : '' ?>">
                                    <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-xs font-semibold">Current</span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="text-center">
                                        <h4 class="text-lg font-semibold mb-2">Professional</h4>
                                        <div class="text-2xl font-bold text-indigo-600 mb-2">₹999</div>
                                        <p class="text-gray-600 mb-4">per school/month</p>
                                        <ul class="text-left space-y-2 mb-6 text-sm">
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Unlimited questions</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Unlimited subjects</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Up to 50 users</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Export & Print</li>
                                            <li><i class="fas fa-check text-green-500 mr-2"></i>Priority support</li>
                                        </ul>
                                        <?php if (isset($current_subscription) && $current_subscription['plan_name'] === 'professional'): ?>
                                            <div class="space-y-2">
                                                <a href="<?= base_url() ?>/subscription/billing" class="block w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700 transition text-center">
                                                    Manage Billing
                                                </a>
                                                <a href="<?= base_url() ?>/subscription/current" class="block w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition text-center">
                                                    View Subscription
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <a href="<?= base_url() ?>/subscription/upgrade" class="block w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700 transition text-center">
                                                Upgrade Now
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Billing History -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Transactions</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <?php if (isset($recent_transactions) && !empty($recent_transactions)): ?>
                                    <div class="space-y-3">
                                        <?php foreach ($recent_transactions as $transaction): ?>
                                        <div class="bg-white rounded-lg p-4 flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-check text-green-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900">
                                                        <?= $transaction['plan_name'] ?? 'Subscription Payment' ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        <?= date('M d, Y', strtotime($transaction['created_at'])) ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="font-semibold text-gray-900">₹<?= number_format($transaction['amount']) ?></div>
                                                <div class="text-sm text-green-600 capitalize"><?= $transaction['status'] ?></div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                        <div class="text-center pt-2">
                                            <a href="<?= base_url() ?>/payment/history" class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                                                View All Transactions →
                                            </a>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-gray-500 py-8">
                                        <i class="fas fa-receipt text-3xl mb-4"></i>
                                        <p>No transactions yet</p>
                                        <p class="text-sm">Your billing history will appear here</p>
                                        <a href="<?= base_url() ?>/subscription/upgrade" class="mt-3 inline-block bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition">
                                            Upgrade Plan
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Settings Section -->
            <div id="settings-section" class="section hidden animate-fade-in" style="min-height: 200px; max-width: 100%; overflow-x: hidden; padding: 0 20px;">
                <!-- Settings Header -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">School Settings</h3>
                                <p class="text-sm text-gray-600 mt-1">Manage your school configuration and preferences</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex space-x-3">
                                <button onclick="saveAllSettings()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                    <i class="fas fa-save mr-2"></i>Update Settings
                                </button>
                                <button onclick="resetSettings()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-undo mr-2"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Navigation Tabs -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-3">
                        <nav class="flex flex-wrap gap-4" aria-label="Settings Tabs">
                            <button onclick="showSettingsTab('school-profile')" class="settings-tab active whitespace-nowrap py-2 px-3 border-b-2 border-indigo-500 font-medium text-sm text-indigo-600">
                                <i class="fas fa-school mr-1"></i>School Profile
                            </button>
                            <button onclick="showSettingsTab('question-bank')" class="settings-tab whitespace-nowrap py-2 px-3 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-question-circle mr-1"></i>Question Bank
                            </button>
                            <button onclick="showSettingsTab('user-management')" class="settings-tab whitespace-nowrap py-2 px-3 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-users mr-1"></i>User Management
                            </button>
                            <button onclick="showSettingsTab('notifications')" class="settings-tab whitespace-nowrap py-2 px-3 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-bell mr-1"></i>Notifications
                            </button>
                            <button onclick="showSettingsTab('system')" class="settings-tab whitespace-nowrap py-2 px-3 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-cogs mr-1"></i>System
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- School Profile Settings Tab -->
                <div id="school-profile-tab" class="settings-tab-content" style="display: block;">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                        <div class="px-6 py-3 border-b border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-800">School Information</h4>
                            <p class="text-sm text-gray-600 mt-1">Update your school's basic information and contact details</p>
                        </div>
                        <div class="p-4">
                            <form id="school-profile-form">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">School Name *</label>
                                        <input type="text" id="school-name" value="<?= isset($school_name) ? esc($school_name) : '' ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-xs text-gray-500">(Read-only)</span></label>
                                        <input type="email" id="school-email" value="<?= isset($school_email) ? esc($school_email) : '' ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg readonly-field" readonly>
                                        <p class="text-xs text-gray-500 mt-1"><i class="fas fa-lock mr-1"></i>Email cannot be changed after registration</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                        <input type="tel" id="school-phone" value="<?= isset($school_phone) ? esc($school_phone) : '' ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Status <span class="text-xs text-gray-500">(System Managed)</span></label>
                                        <select id="school-status" class="w-full px-3 py-2 border border-gray-300 rounded-lg readonly-field" disabled>
                                            <option value="active" <?= (isset($school_status) && $school_status === 'active') ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= (isset($school_status) && $school_status === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                                            <option value="suspended" <?= (isset($school_status) && $school_status === 'suspended') ? 'selected' : '' ?>>Suspended</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1"><i class="fas fa-shield-alt mr-1"></i>Status is managed by system administrator</p>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">School Address</label>
                                    <textarea id="school-address" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Enter complete school address"><?= isset($school_address) ? esc($school_address) : '' ?></textarea>
                                </div>
                                <div class="mt-4 flex justify-end">
                                    <button type="button" onclick="updateSchoolProfile()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                        <i class="fas fa-save mr-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Question Bank Settings Tab -->
                <div id="question-bank-tab" class="settings-tab-content" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Subject Management -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">Subject Management</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure subjects and standards for your school</p>
                            </div>
                            <div class="p-6">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Subjects</label>
                                    <div class="space-y-2" id="subjects-list">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div class="flex items-center">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">Mathematics</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-gray-500">Classes: 1-12</span>
                                                <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div class="flex items-center">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">Science</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-gray-500">Classes: 1-10</span>
                                                <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div class="flex items-center">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">English</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-gray-500">Classes: 1-12</span>
                                                <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="mt-3 w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-indigo-400 hover:text-indigo-600 transition-colors">
                                        <i class="fas fa-plus mr-2"></i>Add New Subject
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Question Approval Settings -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">Question Approval</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure question review and approval process</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Require admin approval for all questions</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">Auto-approve questions from senior staff</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Send notifications for pending reviews</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Auto-approval threshold (days)</label>
                                    <input type="number" value="7" min="1" max="30" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <p class="text-xs text-gray-500 mt-1">Questions pending review for this many days will be auto-approved</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Settings Tab -->
                <div id="user-management-tab" class="settings-tab-content" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- User Registration -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">User Registration</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure how new users can join your school</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Registration Method</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="admin-only">Admin Only</option>
                                        <option value="invite-only">Invitation Only</option>
                                        <option value="open">Open Registration</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Require email verification</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">Allow users to update their own profiles</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Role for New Users</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="teacher">Teacher</option>
                                        <option value="staff">Staff</option>
                                        <option value="guest">Guest</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Password Policy -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">Password Policy</h4>
                                <p class="text-sm text-gray-600 mt-1">Set password requirements for user accounts</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                                    <input type="number" value="8" min="6" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Require uppercase letters</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Require numbers</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">Require special characters</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days)</label>
                                    <input type="number" value="90" min="30" max="365" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <p class="text-xs text-gray-500 mt-1">Set to 0 for no expiry</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Settings Tab -->
                <div id="notifications-tab" class="settings-tab-content" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Email Notifications -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">Email Notifications</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure when to send email notifications</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">New question submissions</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Question approval/rejection</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">New user registrations</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">System maintenance alerts</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">Weekly activity summary</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- In-App Notifications -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">In-App Notifications</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure dashboard notification preferences</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Show notification badges</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Play notification sounds</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-3 text-indigo-600">
                                        <span class="text-sm">Desktop notifications</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Notification Retention (days)</label>
                                    <input type="number" value="30" min="7" max="90" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <p class="text-xs text-gray-500 mt-1">How long to keep notifications in the system</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings Tab -->
                <div id="system-tab" class="settings-tab-content" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- General System Settings -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">General Settings</h4>
                                <p class="text-sm text-gray-600 mt-1">Configure general system preferences</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="Asia/Kolkata" selected>Asia/Kolkata (IST)</option>
                                        <option value="UTC">UTC</option>
                                        <option value="America/New_York">America/New_York (EST)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="DD/MM/YYYY" selected>DD/MM/YYYY</option>
                                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="en" selected>English</option>
                                        <option value="ta">Tamil</option>
                                        <option value="hi">Hindi</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Enable maintenance mode</span>
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">Temporarily disable access for maintenance</p>
                                </div>
                            </div>
                        </div>

                        <!-- Data & Backup Settings -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800">Data & Backup</h4>
                                <p class="text-sm text-gray-600 mt-1">Manage data retention and backup settings</p>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Data Retention Period (months)</label>
                                    <input type="number" value="12" min="6" max="60" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <p class="text-xs text-gray-500 mt-1">How long to keep deleted items in trash</p>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="mr-3 text-indigo-600">
                                        <span class="text-sm">Enable automatic backups</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="daily" selected>Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                    </select>
                                </div>
                                <div class="pt-4 border-t border-gray-200">
                                    <button class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                        <i class="fas fa-download mr-2"></i>Export School Data
                                    </button>
                                    <p class="text-xs text-gray-500 mt-2 text-center">Download a complete backup of your school data</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <!-- Question Review Modal -->
    <!-- Question Review Modal -->
    <div id="questionModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Question Review</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideQuestionModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <!-- Question Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Staff Member</label>
                        <p id="modal-staff-name" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                        <p id="modal-subject" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Standard</label>
                        <p id="modal-standard" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Question Type</label>
                        <p id="modal-question-type" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                        <p id="modal-difficulty" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Marks</label>
                        <p id="modal-marks" class="text-gray-800 bg-gray-50 p-3 rounded-lg">-</p>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                    <div id="modal-question-text" class="text-gray-800 bg-gray-50 p-4 rounded-lg min-h-[100px]">-</div>
                </div>

                <!-- MCQ Options (shown only for MCQ) -->
                <div id="modal-mcq-options" class="mb-6 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Answer Options</label>
                    <div id="modal-options-list" class="space-y-2">
                        <!-- Options will be populated dynamically -->
                    </div>
                </div>

                <!-- Answer/Solution -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Model Answer/Solution</label>
                    <div id="modal-answer" class="text-gray-800 bg-gray-50 p-4 rounded-lg min-h-[100px]">-</div>
                </div>

                <!-- Current Status -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Current Status</label>
                    <div id="modal-status" class="inline-block">
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Pending</span>
                    </div>
                </div>

                <!-- Admin Feedback -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admin Feedback (Optional)</label>
                    <textarea id="admin-feedback" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Add feedback for the staff member..."></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50" onclick="hideQuestionModal()">
                        Close
                    </button>
                    <button id="reject-btn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700" onclick="rejectQuestion()">
                        <i class="fas fa-times mr-2"></i>Reject
                    </button>
                    <button id="approve-btn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700" onclick="approveQuestion()">
                        <i class="fas fa-check mr-2"></i>Approve
                    </button>
                </div>
            </div>
        </div>
    </div>





    <!-- User Details Modal -->
    <div id="userDetailsModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">User Details</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideUserDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <!-- User Avatar -->
                <div class="flex items-center justify-center mb-6">
                    <div id="userAvatar" class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-3xl font-bold">
                        A
                    </div>
                </div>

                <!-- User Details Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 text-gray-700">
                    <!-- Full Name -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-user h-4 w-4"></i>
                            Full Name
                        </span>
                        <div id="userName" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>

                    <!-- Email -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-envelope h-4 w-4"></i>
                            Email Address
                        </span>
                        <div id="userEmail" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>

                    <!-- Designation -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-briefcase h-4 w-4"></i>
                            Designation
                        </span>
                        <div id="userDesignation" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>

                    <!-- Phone -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-phone h-4 w-4"></i>
                            Phone Number
                        </span>
                        <div id="userPhone" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>

                    <!-- Status -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-circle h-4 w-4"></i>
                            Status
                        </span>
                        <div id="userStatus" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>

                    <!-- Created Date -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-blue-100">
                        <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                            <i class="fas fa-calendar h-4 w-4"></i>
                            Joined Date
                        </span>
                        <div id="userCreated" class="text-lg font-semibold text-gray-800">Loading...</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-8 flex justify-center space-x-4">
                    <button class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" onclick="hideUserDetailsModal()">
                        Close
                    </button>
                    <button id="editUserBtn" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Edit User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- All Activity Modal -->
    <div id="allActivityModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">All Recent Activity</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideAllActivityModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="space-y-4" id="all-activity-content">
                    <!-- Activity content will be loaded here -->
                </div>
            </div>
        </div>
    </div>


<script>
// Sidebar toggle for mobile
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('-translate-x-full');
}

// Section navigation
function showSection(sectionName, targetElement = null) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => section.classList.add('hidden'));

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
    }

    // Update navigation
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => {
        link.classList.remove('text-blue-600', 'bg-blue-50', 'border-r-4', 'border-blue-600');
        link.classList.add('text-gray-600');
    });

    // Update active nav link if targetElement is provided
    if (targetElement) {
        targetElement.classList.remove('text-gray-600');
        targetElement.classList.add('text-blue-600', 'bg-blue-50', 'border-r-4', 'border-blue-600');
    } else {
        // Find and activate the corresponding nav link
        const activeLink = document.querySelector(`nav a[onclick*="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.remove('text-gray-600');
            activeLink.classList.add('text-blue-600', 'bg-blue-50', 'border-r-4', 'border-blue-600');
        }
    }

    // Load data for specific sections
    if (sectionName === 'questions') {
        setTimeout(() => {
            loadQuestionsForReview(1);
        }, 100);
    }
}

// Load pending count for navigation badge
function loadPendingCount() {
    fetch('<?= site_url('schooladmin/getQuestionsForReview') ?>?status=pending', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const count = data.questions.length;
            const badge = document.getElementById('pending-count');
            if (count > 0) {
                badge.textContent = count;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }
    })
    .catch(error => {
        console.error('Error loading pending count:', error);
    });
}

// Activity Modal functions
function showAllActivityModal() {
    const modal = document.getElementById('allActivityModal');
    const content = document.getElementById('all-activity-content');

    // Show modal
    modal.classList.remove('hidden');

    // Load all activity data
    loadAllActivity();
}

function hideAllActivityModal() {
    document.getElementById('allActivityModal').classList.add('hidden');
}

function loadAllActivity() {
    const content = document.getElementById('all-activity-content');
    content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-gray-400"></i> Loading...</div>';

    // Get all activity data from PHP
    const allActivity = <?= json_encode($recent_activity ?? []) ?>;

    if (allActivity && allActivity.length > 0) {
        let html = '';
        allActivity.forEach(activity => {
            const timeAgo = formatTimeAgo(activity.time);
            html += `
                <div class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-10 h-10 bg-${activity.color}-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="${activity.icon} text-${activity.color}-600"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">${escapeHtml(activity.title)}</p>
                        <p class="text-sm text-gray-600 mt-1">${escapeHtml(activity.description)}</p>
                        <p class="text-xs text-gray-400 mt-1">${timeAgo}</p>
                    </div>
                </div>
            `;
        });
        content.innerHTML = html;
    } else {
        content.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-clock text-gray-300 text-4xl mb-4"></i>
                <p class="text-gray-500 text-lg font-medium">No recent activity</p>
                <p class="text-gray-400 text-sm mt-2">Activity will appear here as questions are submitted and reviewed</p>
            </div>
        `;
    }
}

function formatTimeAgo(datetime) {
    const now = new Date();
    const time = new Date(datetime);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' min ago';
    if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return hours + ' hour' + (hours > 1 ? 's' : '') + ' ago';
    }
    if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400);
        return days + ' day' + (days > 1 ? 's' : '') + ' ago';
    }
    if (diffInSeconds < 31536000) {
        const months = Math.floor(diffInSeconds / 2592000);
        return months + ' month' + (months > 1 ? 's' : '') + ' ago';
    }
    const years = Math.floor(diffInSeconds / 31536000);
    return years + ' year' + (years > 1 ? 's' : '') + ' ago';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// This initialization is handled in the main DOMContentLoaded event below

// Question Modal functions
function showQuestionModal() {
    document.getElementById('questionModal').classList.remove('hidden');
}

function hideQuestionModal() {
    document.getElementById('questionModal').classList.add('hidden');
}



// User Details Modal functions
function showUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.remove('hidden');
}

function hideUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.add('hidden');
}

// User Modal functions (for adding new users)
function showUserModal() {
    document.getElementById('userModal').classList.remove('hidden');
}

function hideUserModal() {
    document.getElementById('userModal').classList.add('hidden');
}

// Make responsive sidebar work on mobile
window.addEventListener('resize', function() {
    if (window.innerWidth >= 1024) {
        document.getElementById('sidebar').classList.remove('-translate-x-full');
    }
});

// Enhanced Dashboard Functions

// Toggle notifications dropdown
function toggleNotifications() {
    const dropdown = document.getElementById('notifications-dropdown');
    const isVisible = dropdown.style.display !== 'none' && !dropdown.classList.contains('hidden');

    if (isVisible) {
        dropdown.classList.add('hidden');
    } else {
        dropdown.classList.remove('hidden');
        // Close user menu if open
        document.getElementById('userMenu').classList.add('hidden');
    }
}

// Toggle user menu
function toggleUserMenu() {
    const menu = document.getElementById('userMenu');
    const isVisible = menu.style.display !== 'none' && !menu.classList.contains('hidden');

    if (isVisible) {
        menu.classList.add('hidden');
    } else {
        menu.classList.remove('hidden');
        // Close notifications if open
        document.getElementById('notifications-dropdown').classList.add('hidden');
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const notificationBtn = document.getElementById('notification-btn');
    const notificationDropdown = document.getElementById('notifications-dropdown');
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    // Close notifications dropdown
    if (!notificationBtn.contains(event.target) && !notificationDropdown.contains(event.target)) {
        notificationDropdown.classList.add('hidden');
    }

    // Close user menu
    if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
        userMenu.classList.add('hidden');
    }
});

// Enhanced section navigation with page title updates
function showSection(sectionName, targetElement = null) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.add('hidden');
        section.classList.remove('active');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
        targetSection.classList.add('active');
    }

    // Update navigation
    const navLinks = document.querySelectorAll('.nav-item');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Update active nav link
    if (targetElement) {
        targetElement.classList.add('active');
    } else {
        const activeLink = document.querySelector(`[onclick*="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    // Update page title and subtitle
    const titles = {
        'dashboard': 'Dashboard',
        'questions': 'Question Review',
        'staff': 'Staff Management',
        'subscription': 'Subscription Management',
        'settings': 'Settings'
    };

    const subtitles = {
        'dashboard': 'Welcome back, manage your school efficiently',
        'questions': 'Review and approve questions submitted by staff',
        'staff': 'Manage teaching staff and their assignments',
        'subscription': 'Manage your subscription plan and billing',
        'settings': 'Configure your school preferences and system settings'
    };

    document.getElementById('page-title').textContent = titles[sectionName] || 'Dashboard';
    document.getElementById('page-subtitle').textContent = subtitles[sectionName] || 'Welcome back';

    // Load section-specific data
    if (sectionName === 'questions') {
        setTimeout(() => {
            loadStaffForFilter(); // Load staff for the filter dropdown
            loadQuestionsForReview(1);
        }, 100);
    } else if (sectionName === 'staff') {
        setTimeout(() => {
            loadStaffData();
        }, 100);
    } else if (sectionName === 'subscription') {
        setTimeout(() => {
            loadSubscriptionData();
        }, 100);
    } else if (sectionName === 'settings') {
        setTimeout(() => {
            loadSchoolSettings();
        }, 100);
    }
}





// Subscription Management Functions
function loadSubscriptionData() {
    // Subscription data is now loaded from server-side PHP
    console.log('Subscription data loaded from server');
}

function redirectToPayment() {
    // Redirect to payment process with Professional plan
    const planId = 2; // Professional plan ID
    const billingCycle = 'monthly';

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
    button.disabled = true;

    // Redirect to payment process
    window.location.href = `<?= base_url() ?>/payment/process?plan_id=${planId}&billing_cycle=${billingCycle}`;
}

// updateSubscriptionDisplay function removed - data now loaded server-side

// Staff Management Functions

function loadStaffData() {
    console.log('Loading staff data...');
    const url = '<?= site_url('schooladmin/getStaff') ?>';

    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(text => {
        const trimmed = text.trim();

        try {
            const data = JSON.parse(trimmed);
            console.log('Parsed data:', data);
            if (data.success) {
                // Store all staff data globally for filtering
                allStaffData = data.staff;

                displayStaffCards(data.staff);
                updateStaffStats(data.stats);
            } else {
                console.error('Backend error:', data.message);
                showStaffError('Failed to load staff data: ' + (data.message || 'Unknown error'));
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            console.error('Error message:', e.message);
            console.error('Response text:', text);
            showStaffError('Invalid response from server - Check console for details');
        }
    })
    .catch(error => {
        console.error('Network error loading staff:', error);
        showStaffError('Network error loading staff data');
    });
}

function updateStaffStats(stats) {
    console.log('Updating staff stats:', stats);

    // Update total staff count
    const totalStaffElement = document.getElementById('total-staff-count');
    if (totalStaffElement) {
        totalStaffElement.textContent = stats.total_staff || 0;
    }

    // Update active staff count
    const activeStaffElement = document.getElementById('active-staff-count');
    if (activeStaffElement) {
        activeStaffElement.textContent = stats.active_staff || 0;
    }

    // Update inactive staff count
    const inactiveStaffElement = document.getElementById('inactive-staff-count');
    if (inactiveStaffElement) {
        inactiveStaffElement.textContent = stats.inactive_staff || 0;
    }

    // Update teachers count (count staff with teacher-related designations)
    const teachersElement = document.getElementById('teachers-count');
    if (teachersElement && allStaffData) {
        const teacherDesignations = ['Teacher', 'Assistant Teacher', 'Senior Teacher', 'Subject Teacher', 'HOD'];
        const teachersCount = allStaffData.filter(staff =>
            teacherDesignations.some(designation =>
                (staff.designation || '').toLowerCase().includes(designation.toLowerCase())
            )
        ).length;
        teachersElement.textContent = teachersCount;
    }
}

function displayStaffCards(staff) {
    const container = document.getElementById('staff-cards-container');

    if (!staff || staff.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-users text-3xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-700">No staff members found</p>
                <p class="text-sm text-gray-500">Add staff members to get started</p>
                <button onclick="showUserModal()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add First Staff Member
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = staff.map(member => {
        const initials = member.name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['indigo', 'purple', 'blue', 'green', 'yellow', 'red'];
        const color = colors[member.id % colors.length];

        return `
            <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all card-hover">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="w-12 h-12 bg-${color}-100 rounded-full flex items-center justify-center">
                        <span class="text-${color}-600 font-bold text-lg">${initials}</span>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-semibold text-gray-800">${member.name}</h5>
                        <p class="text-sm text-gray-600">${member.designation || 'Staff Member'}</p>
                        <span class="inline-block bg-${member.status === 'active' ? 'green' : 'yellow'}-100 text-${member.status === 'active' ? 'green' : 'yellow'}-800 px-2 py-1 rounded-full text-xs font-medium mt-1">
                            ${member.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
                <div class="space-y-2 text-sm text-gray-600 mb-4">
                    <p><i class="fas fa-envelope mr-2 text-gray-400"></i>${member.email}</p>
                    <p><i class="fas fa-phone mr-2 text-gray-400"></i>${member.phone || 'Not provided'}</p>
                    <p><i class="fas fa-calendar mr-2 text-gray-400"></i>Joined: ${new Date(member.created_at).toLocaleDateString()}</p>
                </div>
                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <p class="text-lg font-bold text-indigo-600">${member.question_count || 0}</p>
                        <p class="text-xs text-gray-600">Questions</p>
                    </div>
                    <div class="text-center">
                        <p class="text-lg font-bold text-green-600">${member.approval_rate || 0}%</p>
                        <p class="text-xs text-gray-600">Approval</p>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="viewStaffProfile(${member.id})" class="text-indigo-600 hover:text-indigo-800 p-2 hover:bg-indigo-50 rounded" title="View Profile">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editStaffProfile(${member.id})" class="text-green-600 hover:text-green-800 p-2 hover:bg-green-50 rounded" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleStaffStatus(${member.id}, '${member.status}')" class="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded" title="${member.status === 'active' ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${member.status === 'active' ? 'user-slash' : 'user-check'}"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function showStaffError(message) {
    const container = document.getElementById('staff-cards-container');
    container.innerHTML = `
        <div class="col-span-full text-center py-12">
            <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
            <p class="text-lg font-medium text-gray-700">${message}</p>
            <button onclick="loadStaffData()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>Retry
            </button>
        </div>
    `;
}







// Initialize dashboard with Chart.js
function initializeDashboard() {
    // Question Status Chart
    const ctx = document.getElementById('questionStatusChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Approved', 'Pending', 'Rejected', 'Draft'],
                datasets: [{
                    data: [<?= isset($stats['approved_questions']) ? $stats['approved_questions'] : '0' ?>,
                           <?= isset($stats['pending_review']) ? $stats['pending_review'] : '0' ?>,
                           <?= isset($stats['rejected_questions']) ? $stats['rejected_questions'] : '0' ?>,
                           <?= isset($stats['draft_questions']) ? $stats['draft_questions'] : '0' ?>],
                    backgroundColor: ['#10B981', '#F59E0B', '#EF4444', '#6B7280'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check for hash in URL to show specific section
    const hash = window.location.hash.substring(1); // Remove the # symbol
    const validSections = ['dashboard', 'questions', 'staff', 'subscription', 'settings'];

    if (hash && validSections.includes(hash)) {
        showSection(hash);
    } else {
        showSection('dashboard');
    }

    loadPendingCount();
    initializeDashboard();

    // Update pending count in quick actions
    setTimeout(() => {
        const pendingCount = document.getElementById('pending-count').textContent;
        const quickPendingCount = document.getElementById('quick-pending-count');
        if (quickPendingCount) {
            quickPendingCount.textContent = pendingCount;
        }
    }, 1000);

    // Refresh pending count every 30 seconds
    setInterval(loadPendingCount, 30000);
});

// Load users function
function loadUsers() {
    fetch('<?= base_url('schooladmin/getUsers') ?>')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('usersTableBody');

            if (data.success && data.data.length > 0) {
                let html = '';
                data.data.forEach(user => {
                    const avatar = user.name.charAt(0).toUpperCase();
                    const designation = user.designation || 'Staff';
                    const phone = user.phone || 'N/A';

                    html += `
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4 flex items-center">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white text-sm font-medium">${avatar}</span>
                                </div>
                                ${user.name}
                            </td>
                            <td class="py-3 px-4">${user.email}</td>
                            <td class="py-3 px-4">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">${designation}</span>
                            </td>
                            <td class="py-3 px-4">
                                <button class="text-blue-600 hover:text-blue-800 mr-3" title="View" onclick="viewUser(${user.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800" title="Delete" onclick="deleteUser(${user.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
                tbody.innerHTML = html;
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="py-8 text-center text-gray-500">
                            No users found. <a href="#" onclick="showUserModal()" class="text-blue-600 hover:underline">Add the first user</a>
                        </td>
                    </tr>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            document.getElementById('usersTableBody').innerHTML = `
                <tr>
                    <td colspan="4" class="py-8 text-center text-red-500">
                        Error loading users. Please refresh the page.
                    </td>
                </tr>
            `;
        });
}

// View user details
function viewUser(userId) {
    console.log('Viewing user:', userId); // Debug log

    fetch(`<?= base_url('schooladmin/getUserDetails') ?>/${userId}`)
        .then(response => {
            console.log('Response status:', response.status); // Debug log
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data); // Debug log

            if (data.success) {
                const user = data.data;

                // Update modal content
                document.getElementById('userAvatar').textContent = user.name.charAt(0).toUpperCase();
                document.getElementById('userName').textContent = user.name;
                document.getElementById('userEmail').textContent = user.email;
                document.getElementById('userDesignation').textContent = user.designation || 'Not specified';
                document.getElementById('userPhone').textContent = user.phone || 'Not provided';
                document.getElementById('userStatus').innerHTML = user.status === 'active'
                    ? '<span class="text-green-600 font-semibold">Active</span>'
                    : '<span class="text-red-600 font-semibold">Inactive</span>';

                // Format created date
                const createdDate = new Date(user.created_at);
                document.getElementById('userCreated').textContent = createdDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                // Show modal
                showUserDetailsModal();
            } else {
                console.error('API Error:', data.message); // Debug log
                alert('Failed to load user details: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            alert('Error loading user details: ' + error.message);
        });
}

// Delete user
function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        fetch(`<?= base_url('schooladmin/deleteUser') ?>/${userId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('User deleted successfully');
                loadUsers(); // Refresh the list
            } else {
                alert(data.message || 'Failed to delete user');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting user');
        });
    }
}

// Function to handle form submission
function submitUserForm(event) {
    event.preventDefault();

    // Get form values
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);

    // Simple validation - check if passwords match
    if (formData.get('password') !== formData.get('confirmPassword')) {
        alert('Passwords do not match!');
        return;
    }

    // Prepare data for API
    const userData = {
        name: formData.get('name'),
        email: formData.get('email'),
        password: formData.get('password'),
        designation: formData.get('designation'),
        phone: formData.get('phone') || ''
    };

    // Send to backend
    fetch('<?= base_url('schooladmin/addUser') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            hideUserModal();

            // Show success message
            alert('User added successfully!');

            // Refresh user list
            loadUsers();

            // Reset form
            form.reset();
        } else {
            // Show error message
            if (data.errors) {
                let errorMsg = 'Validation errors:\n';
                for (let field in data.errors) {
                    errorMsg += `${field}: ${data.errors[field]}\n`;
                }
                alert(errorMsg);
            } else {
                alert(data.message || 'Failed to add user');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the user');
    });
}

// Add these functions if not already present in your script
function showUserModal() {
    document.getElementById('userModal').classList.remove('hidden');
}

function hideUserModal() {
    document.getElementById('userModal').classList.add('hidden');
}

// User menu dropdown functionality
document.getElementById('userMenuBtn').addEventListener('click', function() {
    const menu = document.getElementById('userMenu');
    menu.classList.toggle('hidden');
});

// Close menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('userMenu');
    const button = document.getElementById('userMenuBtn');

    if (!button.contains(event.target) && !menu.contains(event.target)) {
        menu.classList.add('hidden');
    }
});

// Global variable to store current question ID
let currentQuestionId = null;

// Pagination variables
let currentPage = 1;
let totalPages = 1;
let totalQuestions = 0;
let questionsPerPage = 10;

// Load questions for review
function loadQuestionsForReview(page = 1) {
    currentPage = page;
    questionsPerPage = parseInt(document.getElementById('per-page').value) || 10;
    const statusFilter = document.getElementById('review-status-filter').value || 'all';
    const subjectFilter = document.getElementById('subject-filter').value;
    const standardFilter = document.getElementById('standard-filter').value;
    const typeFilter = document.getElementById('type-filter').value;
    const staffFilter = document.getElementById('staff-filter').value;
    const searchQuery = document.getElementById('question-search').value;
    const tableBody = document.getElementById('questions-review-table');

    // Show loading
    tableBody.innerHTML = `
        <tr>
            <td colspan="10" class="text-center py-8 text-gray-500">
                <div class="flex flex-col items-center">
                    <i class="fas fa-spinner fa-spin text-3xl mb-4 text-indigo-500"></i>
                    <p class="text-lg font-medium">Loading questions...</p>
                    <p class="text-sm">Please wait while we fetch the latest data</p>
                </div>
            </td>
        </tr>
    `;

    // Build query parameters
    let url = '<?= site_url('schooladmin/getQuestionsForReview') ?>?status=' + statusFilter;
    url += '&page=' + currentPage;
    url += '&per_page=' + questionsPerPage;
    if (subjectFilter) {
        url += '&subject=' + encodeURIComponent(subjectFilter);
    }
    if (standardFilter) {
        url += '&standard=' + encodeURIComponent(standardFilter);
    }
    if (typeFilter) {
        url += '&type=' + encodeURIComponent(typeFilter);
    }
    if (staffFilter) {
        url += '&staff=' + encodeURIComponent(staffFilter);
    }
    if (searchQuery) {
        url += '&search=' + encodeURIComponent(searchQuery);
    }

    console.log('Making request to:', url);
    console.log('Filters:', {
        status: statusFilter,
        subject: subjectFilter,
        standard: standardFilter,
        type: typeFilter,
        staff: staffFilter,
        search: searchQuery
    });

    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update pagination variables
            totalQuestions = data.total || 0;
            totalPages = Math.ceil(totalQuestions / questionsPerPage);

            if (data.questions.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-12 text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-inbox text-4xl mb-4 text-gray-400"></i>
                                <p class="text-lg font-medium">No questions found</p>
                                <p class="text-sm">Try adjusting your filters or search criteria</p>
                            </div>
                        </td>
                    </tr>
                `;
                updatePaginationInfo();
                updatePaginationControls();
                updateReviewStats(data.stats || {});
                return;
            }

            let html = '';
            data.questions.forEach(question => {
                const statusClass = getStatusClass(question.status);
                const statusText = getStatusText(question.status);
                const questionText = question.question_text.length > 50 ?
                                   question.question_text.substring(0, 50) + '...' :
                                   question.question_text;

                html += `
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="py-4 px-6">
                            <input type="checkbox" class="question-checkbox rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" value="${question.id}">
                        </td>
                        <td class="py-4 px-6" title="${question.question_text}">
                            <div class="max-w-xs">
                                <p class="font-medium text-gray-900">${questionText}</p>
                                <p class="text-sm text-gray-500">${question.chapter || 'No chapter'}</p>
                            </div>
                        </td>
                        <td class="py-4 px-6">
                            <div class="text-sm">
                                <p class="font-medium text-gray-900">${question.staff_name || 'Unknown'}</p>
                            </div>
                        </td>
                        <td class="py-4 px-6">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                ${question.subject}
                            </span>
                        </td>
                        <td class="py-4 px-6">
                            <span class="text-sm font-medium text-gray-900">${question.standard}</span>
                        </td>
                        <td class="py-4 px-6">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                ${getQuestionTypeText(question.question_type)}
                            </span>
                        </td>
                        <td class="py-4 px-6">
                            <span class="text-sm font-medium text-gray-900">${question.marks}</span>
                        </td>
                        <td class="py-4 px-6">
                            <span class="${statusClass}">${statusText}</span>
                        </td>
                        <td class="py-4 px-6">
                            <span class="text-sm text-gray-500">${formatDate(question.created_at)}</span>
                        </td>
                        <td class="py-4 px-6">
                            <div class="flex items-center space-x-2">
                                <button class="text-indigo-600 hover:text-indigo-900 transition-colors" title="View Details" onclick="showQuestionDetails(${question.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${question.status === 'pending' ? `
                                    <button class="text-green-600 hover:text-green-900 transition-colors" title="Approve" onclick="quickApprove(${question.id})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 transition-colors" title="Reject" onclick="quickReject(${question.id})">
                                        <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            });
            tableBody.innerHTML = html;

            // Update pagination
            updatePaginationInfo();
            updatePaginationControls();

            // Update review statistics
            updateReviewStats(data.stats || {});

            // Update export button state
            updateExportButtonState();
        } else {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Error loading questions
                    </td>
                </tr>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading questions:', error);
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-8 text-red-500">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
                        <p class="font-medium">Error loading questions</p>
                        <p class="text-sm text-gray-600 mt-1">${error.message || 'Please try again'}</p>
                    </div>
                </td>
            </tr>
        `;
    });
}

// Pagination functions
function updatePaginationInfo() {
    const startItem = totalQuestions === 0 ? 0 : ((currentPage - 1) * questionsPerPage) + 1;
    const endItem = Math.min(currentPage * questionsPerPage, totalQuestions);

    document.getElementById('pagination-info').textContent =
        `Showing ${startItem} to ${endItem} of ${totalQuestions} questions`;
}

function updatePaginationControls() {
    const paginationControls = document.getElementById('pagination-controls');

    let html = '';

    // Previous button
    const prevDisabled = currentPage <= 1;
    html += `
        <button onclick="goToPage(${currentPage - 1})"
                class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 ${prevDisabled ? 'disabled:opacity-50 disabled:cursor-not-allowed' : ''}"
                ${prevDisabled ? 'disabled' : ''}>
            <i class="fas fa-chevron-left mr-1"></i>Previous
        </button>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        html += `
            <button onclick="goToPage(${i})"
                    class="px-3 py-2 ${isActive ? 'bg-indigo-600 text-white' : 'border border-gray-300 text-gray-600 hover:bg-gray-50'} rounded-lg">
                ${i}
            </button>
        `;
    }

    // Next button
    const nextDisabled = currentPage >= totalPages;
    html += `
        <button onclick="goToPage(${currentPage + 1})"
                class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 ${nextDisabled ? 'disabled:opacity-50 disabled:cursor-not-allowed' : ''}"
                ${nextDisabled ? 'disabled' : ''}>
            Next<i class="fas fa-chevron-right ml-1"></i>
        </button>
    `;

    paginationControls.innerHTML = html;
}

function goToPage(page) {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
        loadQuestionsForReview(page);
    }
}

// Helper functions
function getStatusClass(status) {
    switch(status) {
        case 'approved': return 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs';
        case 'pending': return 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs';
        case 'rejected': return 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs';
        default: return 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Select all checkbox functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const questionCheckboxes = document.querySelectorAll('.question-checkbox');
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateExportButtonState();
        });
    }

    // Update select-all checkbox when individual checkboxes change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('question-checkbox')) {
            const selectAllCheckbox = document.getElementById('select-all');
            const questionCheckboxes = document.querySelectorAll('.question-checkbox');
            const checkedBoxes = document.querySelectorAll('.question-checkbox:checked');

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = questionCheckboxes.length === checkedBoxes.length;
                selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < questionCheckboxes.length;
            }
            updateExportButtonState();
        }
    });
});

// Update export button state based on selected checkboxes
function updateExportButtonState() {
    const selectedCheckboxes = document.querySelectorAll('.question-checkbox:checked');
    const exportBtn = document.getElementById('export-btn');

    if (exportBtn) {
        if (selectedCheckboxes.length > 0) {
            exportBtn.disabled = false;
            exportBtn.innerHTML = `<i class="fas fa-download mr-2"></i>Export Selected (${selectedCheckboxes.length})`;
        } else {
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<i class="fas fa-download mr-2"></i>Export Selected';
        }
    }
}

function getStatusText(status) {
    switch(status) {
        case 'approved': return 'Approved';
        case 'pending': return 'Pending';
        case 'rejected': return 'Rejected';
        default: return status;
    }
}

function getQuestionTypeText(type) {
    switch(type) {
        case 'multiple_choice': return 'MCQ';
        case 'short_answer': return 'Short Answer';
        case 'long_answer': return 'Long Answer';
        case 'essay': return 'Essay';
        default: return type;
    }
}

// Show question details in modal
function showQuestionDetails(questionId) {
    currentQuestionId = questionId;

    fetch(`<?= site_url('schooladmin/getQuestionDetails') ?>/${questionId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const question = data.question;

            // Populate modal fields
            document.getElementById('modal-staff-name').textContent = question.staff_name || 'Unknown';
            document.getElementById('modal-subject').textContent = question.subject;
            document.getElementById('modal-standard').textContent = question.standard;
            document.getElementById('modal-question-type').textContent = getQuestionTypeText(question.question_type);
            document.getElementById('modal-difficulty').textContent = question.difficulty;
            document.getElementById('modal-marks').textContent = question.marks;
            document.getElementById('modal-question-text').textContent = question.question_text;
            document.getElementById('modal-answer').textContent = question.answer || 'No answer provided';

            // Handle MCQ options
            const mcqOptions = document.getElementById('modal-mcq-options');
            const optionsList = document.getElementById('modal-options-list');

            if (question.question_type === 'multiple_choice' && question.options) {
                const options = JSON.parse(question.options);
                let optionsHtml = '';

                ['A', 'B', 'C', 'D'].forEach(letter => {
                    if (options[`option_${letter.toLowerCase()}`]) {
                        const isCorrect = question.correct_answer === letter;
                        optionsHtml += `
                            <div class="flex items-center ${isCorrect ? 'bg-green-50 border border-green-200 rounded-lg p-2' : ''}">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-3">${letter}</span>
                                <span class="text-gray-800">${options[`option_${letter.toLowerCase()}`]}</span>
                                ${isCorrect ? '<i class="fas fa-check text-green-600 ml-auto"></i>' : ''}
                            </div>
                        `;
                    }
                });

                optionsList.innerHTML = optionsHtml;
                mcqOptions.classList.remove('hidden');
            } else {
                mcqOptions.classList.add('hidden');
            }

            // Update status
            const statusElement = document.getElementById('modal-status');
            statusElement.innerHTML = `<span class="${getStatusClass(question.status)}">${getStatusText(question.status)}</span>`;

            // Show/hide action buttons based on status
            const approveBtn = document.getElementById('approve-btn');
            const rejectBtn = document.getElementById('reject-btn');

            if (question.status === 'pending') {
                approveBtn.style.display = 'inline-block';
                rejectBtn.style.display = 'inline-block';
            } else {
                approveBtn.style.display = 'none';
                rejectBtn.style.display = 'none';
            }

            // Clear previous feedback
            document.getElementById('admin-feedback').value = question.admin_feedback || '';

            // Show modal
            document.getElementById('questionModal').classList.remove('hidden');
        } else {
            alert('Error loading question details: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading question details');
    });
}

// Hide question modal
function hideQuestionModal() {
    document.getElementById('questionModal').classList.add('hidden');
    currentQuestionId = null;
}

// Approve question
function approveQuestion() {
    if (!currentQuestionId) return;

    const feedback = document.getElementById('admin-feedback').value;

    if (confirm('Are you sure you want to approve this question?')) {
        const formData = new FormData();
        formData.append('feedback', feedback);

        fetch(`<?= site_url('schooladmin/approveQuestion') ?>/${currentQuestionId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Question approved successfully!');
                hideQuestionModal();
                loadQuestionsForReview(currentPage);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving question');
        });
    }
}

// Reject question
function rejectQuestion() {
    if (!currentQuestionId) return;

    const feedback = document.getElementById('admin-feedback').value;

    if (!feedback.trim()) {
        alert('Please provide feedback for rejection');
        return;
    }

    if (confirm('Are you sure you want to reject this question?')) {
        const formData = new FormData();
        formData.append('feedback', feedback);

        fetch(`<?= site_url('schooladmin/rejectQuestion') ?>/${currentQuestionId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Question rejected successfully!');
                hideQuestionModal();
                loadQuestionsForReview(currentPage);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error rejecting question');
        });
    }
}

// Quick approve (without modal)
function quickApprove(questionId) {
    if (confirm('Are you sure you want to approve this question?')) {
        const formData = new FormData();
        formData.append('feedback', '');

        fetch(`<?= site_url('schooladmin/approveQuestion') ?>/${questionId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Question approved successfully!');
                loadQuestionsForReview(currentPage);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving question');
        });
    }
}

// Quick reject (with prompt for feedback)
function quickReject(questionId) {
    const feedback = prompt('Please provide feedback for rejection:');

    if (feedback === null) return; // User cancelled

    if (!feedback.trim()) {
        alert('Feedback is required for rejection');
        return;
    }

    const formData = new FormData();
    formData.append('feedback', feedback);

    fetch(`<?= site_url('schooladmin/rejectQuestion') ?>/${questionId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Question rejected successfully!');
            loadQuestionsForReview(currentPage);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error rejecting question');
    });
}



// Additional utility functions

function clearFilters() {
    document.getElementById('question-search').value = '';
    document.getElementById('subject-filter').value = '';
    document.getElementById('standard-filter').value = '';
    document.getElementById('type-filter').value = '';
    document.getElementById('staff-filter').value = '';
    loadQuestionsForReview(1); // Reset to first page when clearing filters
}

// Debounce search function
let searchTimeout;
function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        loadQuestionsForReview(1);
    }, 500);
}

// Load staff members for the staff filter dropdown
function loadStaffForFilter() {
    fetch('<?= site_url('schooladmin/getStaffMembers') ?>', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const staffFilter = document.getElementById('staff-filter');
            // Clear existing options except "All Staff"
            staffFilter.innerHTML = '<option value="">All Staff</option>';

            // Add staff options
            data.staff.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = staff.name;
                staffFilter.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading staff members:', error);
    });
}

// Export selected questions functionality
function exportSelectedQuestions() {
    const selectedCheckboxes = document.querySelectorAll('.question-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        showNotification('Please select questions to export', 'warning');
        return;
    }

    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    // Show loading state
    const exportBtn = document.getElementById('export-btn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Exporting...';
    exportBtn.disabled = true;

    // Create form and submit for download
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= site_url('schooladmin/exportQuestions') ?>';
    form.style.display = 'none';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    // Add selected question IDs
    selectedIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'question_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    // Reset button state
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        showNotification(`${selectedIds.length} questions exported successfully`, 'success');
    }, 1000);
}

// Update review statistics
function updateReviewStats(stats) {
    document.getElementById('pending-stats').textContent = stats.pending || 0;
    document.getElementById('approved-stats').textContent = stats.approved || 0;
    document.getElementById('rejected-stats').textContent = stats.rejected || 0;
    document.getElementById('total-stats').textContent = stats.total || 0;
}



function bulkApprove() {
    console.log('Bulk approving questions...');
    alert('Selected questions approved successfully!');
}

function bulkReject() {
    console.log('Bulk rejecting questions...');
    alert('Selected questions rejected successfully!');
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('bulk-actions').classList.add('hidden');
}

function clearStaffFilters() {
    document.getElementById('staff-search').value = '';
    document.getElementById('designation-filter').value = '';
    document.getElementById('status-filter').value = '';
    filterStaff(); // Apply filters after clearing
}

// Global variable to store all staff data for filtering
let allStaffData = [];

function filterStaff() {
    const searchTerm = document.getElementById('staff-search').value.toLowerCase();
    const designationFilter = document.getElementById('designation-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Filter the staff data
    const filteredStaff = allStaffData.filter(staff => {
        // Search filter (name, email, designation)
        const matchesSearch = !searchTerm ||
            staff.name.toLowerCase().includes(searchTerm) ||
            staff.email.toLowerCase().includes(searchTerm) ||
            (staff.designation && staff.designation.toLowerCase().includes(searchTerm));

        // Designation filter
        const matchesDesignation = !designationFilter ||
            (staff.designation && staff.designation === designationFilter);

        // Status filter
        const matchesStatus = !statusFilter || staff.status === statusFilter;

        return matchesSearch && matchesDesignation && matchesStatus;
    });

    // Display filtered results
    displayStaffCards(filteredStaff);

    // Update stats for filtered results
    updateFilteredStats(filteredStaff);
}

function updateFilteredStats(filteredStaff) {
    const totalStaff = filteredStaff.length;
    const activeStaff = filteredStaff.filter(staff => staff.status === 'active').length;
    const inactiveStaff = totalStaff - activeStaff;

    // Update the stats display
    updateStaffStats({
        total_staff: totalStaff,
        active_staff: activeStaff,
        inactive_staff: inactiveStaff
    });
}



// Staff Profile Actions
function viewStaffProfile(staffId) {
    const staff = allStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found!', 'error');
        return;
    }

    // Create and show staff profile modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">Staff Profile</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="flex items-center space-x-6 mb-6">
                    <div class="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-indigo-600 font-bold text-2xl">${staff.name.split(' ').map(n => n[0]).join('').toUpperCase()}</span>
                    </div>
                    <div>
                        <h4 class="text-2xl font-bold text-gray-800">${staff.name}</h4>
                        <p class="text-lg text-gray-600">${staff.designation || 'Staff Member'}</p>
                        <span class="inline-block bg-${staff.status === 'active' ? 'green' : 'yellow'}-100 text-${staff.status === 'active' ? 'green' : 'yellow'}-800 px-3 py-1 rounded-full text-sm font-medium mt-2">
                            ${staff.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h5 class="font-semibold text-gray-800 border-b pb-2">Contact Information</h5>
                        <div class="space-y-3">
                            <p class="flex items-center"><i class="fas fa-envelope mr-3 text-gray-400 w-5"></i><span class="text-gray-700">${staff.email}</span></p>
                            <p class="flex items-center"><i class="fas fa-phone mr-3 text-gray-400 w-5"></i><span class="text-gray-700">${staff.phone || 'Not provided'}</span></p>
                            <p class="flex items-center"><i class="fas fa-calendar mr-3 text-gray-400 w-5"></i><span class="text-gray-700">Joined: ${new Date(staff.created_at).toLocaleDateString()}</span></p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h5 class="font-semibold text-gray-800 border-b pb-2">Performance Stats</h5>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Questions Created:</span>
                                <span class="font-semibold text-indigo-600">${staff.question_count || 0}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Approval Rate:</span>
                                <span class="font-semibold text-green-600">${staff.approval_rate || 0}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Account Status:</span>
                                <span class="font-semibold ${staff.status === 'active' ? 'text-green-600' : 'text-yellow-600'}">${staff.status === 'active' ? 'Active' : 'Inactive'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="editStaffProfile(${staff.id}); this.closest('.fixed').remove();" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Profile
                    </button>
                    <button onclick="this.closest('.fixed').remove()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function editStaffProfile(staffId) {
    const staff = allStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found!', 'error');
        return;
    }

    // Create and show edit staff modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-xl w-full max-w-lg max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">Edit Staff Profile</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form onsubmit="updateStaffProfile(event, ${staff.id})" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="edit-staff-name" value="${staff.name}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="edit-staff-email" value="${staff.email}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <input type="tel" id="edit-staff-phone" value="${staff.phone || ''}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                        <select id="edit-staff-designation" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                            <option value="">Select Designation</option>
                            <option value="Principal" ${staff.designation === 'Principal' ? 'selected' : ''}>Principal</option>
                            <option value="Vice Principal" ${staff.designation === 'Vice Principal' ? 'selected' : ''}>Vice Principal</option>
                            <option value="HOD" ${staff.designation === 'HOD' ? 'selected' : ''}>HOD</option>
                            <option value="Teacher" ${staff.designation === 'Teacher' ? 'selected' : ''}>Teacher</option>
                            <option value="Assistant Teacher" ${staff.designation === 'Assistant Teacher' ? 'selected' : ''}>Assistant Teacher</option>
                            <option value="Admin Staff" ${staff.designation === 'Admin Staff' ? 'selected' : ''}>Admin Staff</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="edit-staff-status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                            <option value="active" ${staff.status === 'active' ? 'selected' : ''}>Active</option>
                            <option value="inactive" ${staff.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                            <option value="on-leave" ${staff.status === 'on-leave' ? 'selected' : ''}>On Leave</option>
                            <option value="suspended" ${staff.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="this.closest('.fixed').remove()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>Update Profile
                    </button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);
}

function updateStaffProfile(event, staffId) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('edit-staff-name').value,
        email: document.getElementById('edit-staff-email').value,
        phone: document.getElementById('edit-staff-phone').value,
        designation: document.getElementById('edit-staff-designation').value,
        status: document.getElementById('edit-staff-status').value
    };

    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    submitBtn.disabled = true;

    // Send update request to server
    fetch('<?= base_url('schooladmin/updateStaff') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            staff_id: staffId,
            ...formData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the staff data in memory
            const staffIndex = allStaffData.findIndex(s => s.id == staffId);
            if (staffIndex !== -1) {
                allStaffData[staffIndex] = { ...allStaffData[staffIndex], ...formData };
            }

            // Refresh the display
            filterStaff();

            // Close modal
            event.target.closest('.fixed').remove();

            // Show success message
            showNotification('Staff profile updated successfully!', 'success');
        } else {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

            // Show error message
            showNotification(data.message || 'Failed to update staff profile!', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating staff:', error);

        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Show error message
        showNotification('Network error. Please try again.', 'error');
    });
}

function toggleStaffStatus(staffId, currentStatus) {
    const staff = allStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found!', 'error');
        return;
    }

    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${action} ${staff.name}?`)) {
        // Send status update request to server
        fetch('<?= base_url('schooladmin/updateStaff') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                staff_id: staffId,
                name: staff.name,
                email: staff.email,
                phone: staff.phone,
                designation: staff.designation,
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the staff status in memory
                const staffIndex = allStaffData.findIndex(s => s.id == staffId);
                if (staffIndex !== -1) {
                    allStaffData[staffIndex].status = newStatus;
                }

                // Refresh the display
                filterStaff();

                // Show success message
                showNotification(`${staff.name} has been ${action}d successfully!`, 'success');
            } else {
                showNotification(data.message || `Failed to ${action} staff member!`, 'error');
            }
        })
        .catch(error => {
            console.error('Error updating staff status:', error);
            showNotification('Network error. Please try again.', 'error');
        });
    }
}











function changePerPage() {
    questionsPerPage = parseInt(document.getElementById('per-page').value) || 10;
    loadQuestionsForReview(1); // Reset to first page when changing per-page
}

// Settings functionality
function showSettingsTab(tabName) {
    // Hide all settings tab contents
    const tabContents = document.querySelectorAll('.settings-tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
        content.classList.add('hidden');
    });

    // Show selected tab content
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.style.display = 'block';
        targetTab.classList.remove('hidden');
    }

    // Update tab navigation
    const tabs = document.querySelectorAll('.settings-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active', 'border-indigo-500', 'text-indigo-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });

    // Activate selected tab
    const activeTab = document.querySelector(`[onclick="showSettingsTab('${tabName}')"]`);
    if (activeTab) {
        activeTab.classList.remove('border-transparent', 'text-gray-500');
        activeTab.classList.add('active', 'border-indigo-500', 'text-indigo-600');
    }
}

function updateSchoolProfile() {
    const formData = {
        name: document.getElementById('school-name').value,
        email: document.getElementById('school-email').value,
        phone: document.getElementById('school-phone').value,
        address: document.getElementById('school-address').value
    };

    // Validate required fields
    if (!formData.name || !formData.email) {
        showNotification('Please fill in all required fields (School Name and Email)', 'error');
        return;
    }

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    button.disabled = true;

    // Make API call
    fetch('<?= site_url('schooladmin/updateSchoolProfile') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(formData)
    })
    .then(response => response.json())
    .then(data => {
        // Reset button state
        button.innerHTML = originalText;
        button.disabled = false;

        if (data.success) {
            showNotification(data.message, 'success');
            // Update the page title if school name changed
            if (formData.name) {
                document.querySelector('h1').textContent = formData.name;
            }
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        // Reset button state
        button.innerHTML = originalText;
        button.disabled = false;

        console.error('Error:', error);
        showNotification('An error occurred while updating the profile', 'error');
    });
}

function saveAllSettings() {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    button.disabled = true;

    // Collect all settings data
    const settingsData = {
        school_profile: {
            name: document.getElementById('school-name').value,
            email: document.getElementById('school-email').value,
            phone: document.getElementById('school-phone').value,
            address: document.getElementById('school-address').value
        },
        // Add other settings as needed
    };

    // Make API call
    fetch('<?= site_url('schooladmin/saveAllSettings') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData)
    })
    .then(response => response.json())
    .then(data => {
        // Reset button state
        button.innerHTML = originalText;
        button.disabled = false;

        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        // Reset button state
        button.innerHTML = originalText;
        button.disabled = false;

        console.error('Error:', error);
        showNotification('An error occurred while saving settings', 'error');
    });
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';
        button.disabled = true;

        // Simulate API call (replace with actual API endpoint)
        setTimeout(() => {
            // Reset button state
            button.innerHTML = originalText;
            button.disabled = false;

            // Reload the page to show default values
            location.reload();
        }, 1500);
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function loadSchoolSettings() {
    fetch('<?= site_url('schooladmin/getSchoolSettings') ?>')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const settings = data.data;

            // Populate school profile fields
            if (settings.school_profile) {
                document.getElementById('school-name').value = settings.school_profile.name || '';
                document.getElementById('school-email').value = settings.school_profile.email || '';
                document.getElementById('school-phone').value = settings.school_profile.phone || '';
                document.getElementById('school-address').value = settings.school_profile.address || '';
                document.getElementById('school-status').value = settings.school_profile.status || 'active';
            }

            // You can populate other settings tabs here as needed
            console.log('Settings loaded:', settings);
        } else {
            console.error('Failed to load settings:', data.message);
            showNotification('Failed to load settings', 'error');
        }
    })
    .catch(error => {
        console.error('Error loading settings:', error);
        showNotification('Error loading settings', 'error');
    });
}

// Initialize settings on page load
document.addEventListener('DOMContentLoaded', function() {
    // Show default settings tab
    showSettingsTab('school-profile');
});

// Navigation functions for dashboard cards
function navigateToQuestions() {
    // Show the questions section
    showSection('questions');
}

function navigateToPendingReview() {
    // Show questions section and set filter to pending
    showSection('questions');
    // Wait for section to load, then set the filter
    setTimeout(() => {
        const statusFilter = document.getElementById('review-status-filter');
        if (statusFilter) {
            statusFilter.value = 'pending';
            loadQuestionsForReview(1); // Reload with pending filter
        }
    }, 200);
}

function navigateToStaff() {
    // Show the staff management section
    showSection('staff');
}

function navigateToApprovalStats() {
    // Show questions section (which contains approval statistics)
    showSection('questions');
    // Optionally scroll to stats area if it exists
    setTimeout(() => {
        const statsElement = document.querySelector('.question-stats, #question-status-overview');
        if (statsElement) {
            statsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }, 300);
}

</script>
</body>
</html>